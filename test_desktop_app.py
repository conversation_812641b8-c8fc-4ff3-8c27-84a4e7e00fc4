#!/usr/bin/env python3
"""
Cash Register Pro - Desktop Application Test Suite
Tests all functionality including login, styling, and backend connectivity
"""

import os
import sys
import time
import requests
import subprocess
from pathlib import Path
import json

class DesktopAppTester:
    def __init__(self):
        self.root_dir = Path.cwd()
        self.backend_url = "http://localhost:5000"
        self.test_results = []
        
    def print_header(self):
        """Print test header"""
        print("=" * 70)
        print("    CASH REGISTER PRO - DESKTOP APPLICATION TEST SUITE")
        print("=" * 70)
        print()
        
    def log_test(self, test_name, passed, details=""):
        """Log test result"""
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{status} {test_name}")
        if details:
            print(f"    {details}")
        
        self.test_results.append({
            'test': test_name,
            'passed': passed,
            'details': details
        })
        
    def test_file_structure(self):
        """Test if all required files exist"""
        print("🔍 Testing File Structure...")
        
        required_files = [
            'CashRegisterPro.py',
            'desktop_launcher.py',
            'system_checker.py',
            'installer.py',
            'frontend/index.html',
            'frontend/styles.css',
            'frontend/renderer.js',
            'frontend/main.js',
            'frontend/package.json',
            'backend/app.py',
            'backend/api/auth.py'
        ]
        
        missing_files = []
        for file_path in required_files:
            full_path = self.root_dir / file_path
            if full_path.exists():
                self.log_test(f"File exists: {file_path}", True)
            else:
                self.log_test(f"File exists: {file_path}", False, "File not found")
                missing_files.append(file_path)
                
        return len(missing_files) == 0
        
    def test_backend_connectivity(self):
        """Test backend server connectivity"""
        print("\n🔗 Testing Backend Connectivity...")
        
        try:
            response = requests.get(f"{self.backend_url}/", timeout=5)
            if response.status_code == 200:
                self.log_test("Backend server responding", True, f"Status: {response.status_code}")
                return True
            else:
                self.log_test("Backend server responding", False, f"Status: {response.status_code}")
                return False
        except requests.exceptions.ConnectionError:
            self.log_test("Backend server responding", False, "Connection refused - server not running")
            return False
        except Exception as e:
            self.log_test("Backend server responding", False, f"Error: {e}")
            return False
            
    def test_auth_endpoints(self):
        """Test authentication endpoints"""
        print("\n🔐 Testing Authentication Endpoints...")
        
        # Test login endpoint
        try:
            login_data = {
                "username": "admin",
                "password": "admin123"
            }
            
            response = requests.post(f"{self.backend_url}/api/auth/login", 
                                   json=login_data, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                if 'token' in data:
                    self.log_test("Login endpoint", True, "Token received")
                    return data['token']
                else:
                    self.log_test("Login endpoint", False, "No token in response")
                    return None
            else:
                self.log_test("Login endpoint", False, f"Status: {response.status_code}")
                return None
                
        except Exception as e:
            self.log_test("Login endpoint", False, f"Error: {e}")
            return None
            
    def test_electron_app(self):
        """Test if Electron app exists and can be launched"""
        print("\n⚡ Testing Electron Desktop Application...")
        
        frontend_dir = self.root_dir / "frontend"
        possible_paths = [
            frontend_dir / "dist" / "win-unpacked" / "CashRegisterApp.exe",
            frontend_dir / "dist" / "win-unpacked" / "Cash Register Pro.exe",
            frontend_dir / "dist" / "CashRegisterApp.exe"
        ]
        
        electron_found = False
        for path in possible_paths:
            if path.exists():
                self.log_test(f"Electron app found", True, f"Path: {path}")
                electron_found = True
                break
                
        if not electron_found:
            self.log_test("Electron app found", False, "No Electron executable found")
            
        # Test npm availability
        try:
            result = subprocess.run(['npm', '--version'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                self.log_test("npm available", True, f"Version: {result.stdout.strip()}")
            else:
                self.log_test("npm available", False, "npm command failed")
        except:
            self.log_test("npm available", False, "npm not found")
            
        return electron_found
        
    def test_frontend_files(self):
        """Test frontend file integrity"""
        print("\n🎨 Testing Frontend Files...")
        
        # Test HTML structure
        index_file = self.root_dir / "frontend" / "index.html"
        if index_file.exists():
            content = index_file.read_text()
            
            required_elements = [
                'id="login-btn"',
                'id="username"',
                'id="password"',
                'class="login-form"',
                'class="floating-elements"',
                'class="logo-container"'
            ]
            
            for element in required_elements:
                if element in content:
                    self.log_test(f"HTML element: {element}", True)
                else:
                    self.log_test(f"HTML element: {element}", False, "Element not found")
                    
        # Test CSS file
        css_file = self.root_dir / "frontend" / "styles.css"
        if css_file.exists():
            content = css_file.read_text()
            
            required_styles = [
                '.login-container',
                '.floating-elements',
                '.logo-container',
                '.btn-ripple',
                '@keyframes gradientShift',
                '@keyframes floatAround'
            ]
            
            for style in required_styles:
                if style in content:
                    self.log_test(f"CSS style: {style}", True)
                else:
                    self.log_test(f"CSS style: {style}", False, "Style not found")
                    
        # Test JavaScript file
        js_file = self.root_dir / "frontend" / "renderer.js"
        if js_file.exists():
            content = js_file.read_text()
            
            required_functions = [
                'testBackendConnection',
                'handleLogin',
                'async function login',
                'addEventListener'
            ]
            
            for func in required_functions:
                if func in content:
                    self.log_test(f"JS function: {func}", True)
                else:
                    self.log_test(f"JS function: {func}", False, "Function not found")
                    
    def test_styling_enhancements(self):
        """Test if styling enhancements are present"""
        print("\n✨ Testing Styling Enhancements...")
        
        css_file = self.root_dir / "frontend" / "styles.css"
        if css_file.exists():
            content = css_file.read_text()
            
            enhancements = [
                ('Gradient animations', '@keyframes gradientShift'),
                ('Floating elements', '.floating-element'),
                ('Enhanced buttons', '.btn-ripple'),
                ('Logo animations', '@keyframes logoFloat'),
                ('Ring effects', '.ring'),
                ('Input focus effects', '.input-focus-border'),
                ('Particle effects', '@keyframes particleFloat'),
                ('Success animations', '@keyframes successPulse'),
                ('Enhanced shadows', '--shadow-glow'),
                ('Responsive design', '@media (max-width: 768px)')
            ]
            
            for name, selector in enhancements:
                if selector in content:
                    self.log_test(f"Enhancement: {name}", True)
                else:
                    self.log_test(f"Enhancement: {name}", False, "Enhancement not found")
                    
    def test_desktop_launcher(self):
        """Test desktop launcher functionality"""
        print("\n🖥️  Testing Desktop Launcher...")
        
        launcher_file = self.root_dir / "desktop_launcher.py"
        if launcher_file.exists():
            content = launcher_file.read_text()
            
            required_features = [
                ('Splash screen', 'show_splash_screen'),
                ('Backend startup', 'start_backend'),
                ('Electron detection', 'find_electron_app'),
                ('Error handling', 'show_error_dialog'),
                ('Shortcut creation', 'create_shortcuts')
            ]
            
            for name, feature in required_features:
                if feature in content:
                    self.log_test(f"Launcher feature: {name}", True)
                else:
                    self.log_test(f"Launcher feature: {name}", False, "Feature not found")
                    
    def run_comprehensive_test(self):
        """Run all tests"""
        self.print_header()
        
        print("🚀 Starting comprehensive desktop application test...")
        print()
        
        # Run all tests
        tests = [
            ("File Structure", self.test_file_structure),
            ("Backend Connectivity", self.test_backend_connectivity),
            ("Authentication", self.test_auth_endpoints),
            ("Electron App", self.test_electron_app),
            ("Frontend Files", self.test_frontend_files),
            ("Styling Enhancements", self.test_styling_enhancements),
            ("Desktop Launcher", self.test_desktop_launcher)
        ]
        
        passed_tests = 0
        total_tests = len(tests)
        
        for test_name, test_func in tests:
            try:
                result = test_func()
                if result is not False:  # None or True counts as pass
                    passed_tests += 1
            except Exception as e:
                self.log_test(f"Test execution: {test_name}", False, f"Exception: {e}")
                
        # Print summary
        self.print_test_summary(passed_tests, total_tests)
        
        return passed_tests == total_tests
        
    def print_test_summary(self, passed, total):
        """Print test summary"""
        print("\n" + "=" * 70)
        print("    TEST SUMMARY")
        print("=" * 70)
        
        success_rate = (passed / total) * 100 if total > 0 else 0
        
        if success_rate >= 90:
            print("🎉 EXCELLENT! Your desktop application is working perfectly!")
        elif success_rate >= 75:
            print("✅ GOOD! Your desktop application is mostly working.")
        elif success_rate >= 50:
            print("⚠️  PARTIAL! Some components need attention.")
        else:
            print("❌ ISSUES! Several components need fixing.")
            
        print(f"\n📊 Test Results: {passed}/{total} tests passed ({success_rate:.1f}%)")
        
        # Count individual test results
        individual_passed = sum(1 for result in self.test_results if result['passed'])
        individual_total = len(self.test_results)
        
        print(f"📋 Individual Checks: {individual_passed}/{individual_total} passed")
        
        if success_rate >= 90:
            print("\n🚀 Your Cash Register Pro desktop application is ready for use!")
            print("   • Enhanced styling and animations are working")
            print("   • Login functionality is operational")
            print("   • Backend connectivity is established")
            print("   • Desktop application is properly configured")
        else:
            print("\n💡 Recommendations:")
            failed_tests = [r for r in self.test_results if not r['passed']]
            for test in failed_tests[:5]:  # Show first 5 failures
                print(f"   • Fix: {test['test']}")
                
        print("=" * 70)

def main():
    """Main function"""
    tester = DesktopAppTester()
    
    success = tester.run_comprehensive_test()
    
    print()
    input("Press Enter to exit...")
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
