#!/usr/bin/env python3
"""
Cash Register Pro - Complete Build Script
Builds the entire application into a single executable
"""

import os
import sys
import subprocess
import shutil
import time
from pathlib import Path

class CashRegisterBuilder:
    def __init__(self):
        self.root_dir = Path(__file__).parent
        self.frontend_dir = self.root_dir / "frontend"
        self.backend_dir = self.root_dir / "backend"
        self.dist_dir = self.root_dir / "dist"
        
    def print_step(self, step, message):
        print(f"\n{'='*60}")
        print(f"STEP {step}: {message}")
        print(f"{'='*60}")
        
    def run_command(self, command, cwd=None, shell=True):
        """Run a command and return success status"""
        try:
            print(f"Running: {command}")
            if cwd:
                print(f"In directory: {cwd}")
            
            result = subprocess.run(
                command, 
                shell=shell, 
                cwd=cwd, 
                capture_output=True, 
                text=True
            )
            
            if result.returncode == 0:
                print("✓ Command completed successfully")
                if result.stdout:
                    print("Output:", result.stdout[:500])  # Limit output
                return True
            else:
                print("✗ Command failed")
                print("Error:", result.stderr)
                return False
                
        except Exception as e:
            print(f"✗ Exception running command: {e}")
            return False
    
    def check_dependencies(self):
        """Check if required tools are installed"""
        self.print_step(1, "Checking Dependencies")
        
        dependencies = [
            ("python", "python --version"),
            ("node", "node --version"),
            ("npm", "npm --version")
        ]
        
        missing = []
        for name, command in dependencies:
            if not self.run_command(command):
                missing.append(name)
        
        if missing:
            print(f"\n❌ Missing dependencies: {', '.join(missing)}")
            print("Please install the missing dependencies and try again.")
            return False
        
        print("\n✅ All dependencies are available")
        return True
    
    def install_frontend_dependencies(self):
        """Install frontend dependencies"""
        self.print_step(2, "Installing Frontend Dependencies")
        
        if not self.frontend_dir.exists():
            print("❌ Frontend directory not found!")
            return False
        
        # Install npm dependencies
        if not self.run_command("npm install", cwd=self.frontend_dir):
            print("❌ Failed to install frontend dependencies")
            return False
        
        print("✅ Frontend dependencies installed")
        return True
    
    def install_backend_dependencies(self):
        """Install backend dependencies"""
        self.print_step(3, "Installing Backend Dependencies")
        
        if not self.backend_dir.exists():
            print("❌ Backend directory not found!")
            return False
        
        # Install Python dependencies
        requirements = [
            "flask",
            "flask-sqlalchemy", 
            "flask-cors",
            "pyjwt",
            "werkzeug"
        ]
        
        for req in requirements:
            if not self.run_command(f"pip install {req}"):
                print(f"❌ Failed to install {req}")
                return False
        
        print("✅ Backend dependencies installed")
        return True
    
    def prepare_build_directory(self):
        """Prepare the build directory"""
        self.print_step(4, "Preparing Build Directory")
        
        # Clean existing dist directory
        if self.dist_dir.exists():
            print("Cleaning existing dist directory...")
            shutil.rmtree(self.dist_dir)
        
        # Create fresh dist directory
        self.dist_dir.mkdir(exist_ok=True)
        
        print("✅ Build directory prepared")
        return True
    
    def build_electron_app(self):
        """Build the Electron application"""
        self.print_step(5, "Building Electron Application")
        
        # Build for Windows
        if not self.run_command("npm run dist:win", cwd=self.frontend_dir):
            print("❌ Failed to build Electron application")
            return False
        
        print("✅ Electron application built successfully")
        return True
    
    def copy_backend_to_dist(self):
        """Copy backend files to distribution"""
        self.print_step(6, "Copying Backend Files")
        
        try:
            # Find the built Electron app directory
            electron_dist = self.frontend_dir / "dist"
            if not electron_dist.exists():
                print("❌ Electron dist directory not found!")
                return False
            
            # Find the unpacked directory
            unpacked_dirs = list(electron_dist.glob("win-unpacked"))
            if not unpacked_dirs:
                print("❌ Electron unpacked directory not found!")
                return False
            
            unpacked_dir = unpacked_dirs[0]
            backend_dest = unpacked_dir / "resources" / "backend"
            
            # Copy backend files
            if backend_dest.exists():
                shutil.rmtree(backend_dest)
            
            shutil.copytree(self.backend_dir, backend_dest, 
                          ignore=shutil.ignore_patterns('__pycache__', '*.pyc', 'venv', '.git'))
            
            print("✅ Backend files copied to distribution")
            return True
            
        except Exception as e:
            print(f"❌ Error copying backend files: {e}")
            return False
    
    def create_launcher_script(self):
        """Create a launcher script that starts both backend and frontend"""
        self.print_step(7, "Creating Launcher Script")
        
        try:
            # Find the unpacked directory
            electron_dist = self.frontend_dir / "dist"
            unpacked_dirs = list(electron_dist.glob("win-unpacked"))
            if not unpacked_dirs:
                print("❌ Electron unpacked directory not found!")
                return False
            
            unpacked_dir = unpacked_dirs[0]
            
            # Create launcher batch file
            launcher_content = '''@echo off
echo Starting Cash Register Pro...
echo.

REM Start the backend server
echo Starting backend server...
cd /d "%~dp0resources\\backend"
start /B python app.py

REM Wait for backend to start
echo Waiting for backend to initialize...
timeout /t 3 /nobreak >nul

REM Start the frontend
echo Starting frontend application...
cd /d "%~dp0"
start "" "Cash Register Pro.exe"

echo Cash Register Pro is starting...
echo You can close this window once the application opens.
pause
'''
            
            launcher_path = unpacked_dir / "Start Cash Register Pro.bat"
            with open(launcher_path, 'w') as f:
                f.write(launcher_content)
            
            print("✅ Launcher script created")
            return True
            
        except Exception as e:
            print(f"❌ Error creating launcher script: {e}")
            return False
    
    def create_final_package(self):
        """Create the final distribution package"""
        self.print_step(8, "Creating Final Package")
        
        try:
            # Find the unpacked directory
            electron_dist = self.frontend_dir / "dist"
            unpacked_dirs = list(electron_dist.glob("win-unpacked"))
            if not unpacked_dirs:
                print("❌ Electron unpacked directory not found!")
                return False
            
            unpacked_dir = unpacked_dirs[0]
            
            # Copy to final dist directory
            final_dist = self.dist_dir / "Cash Register Pro"
            if final_dist.exists():
                shutil.rmtree(final_dist)
            
            shutil.copytree(unpacked_dir, final_dist)
            
            # Create README
            readme_content = '''# Cash Register Pro

## Installation Instructions

1. Extract all files to a folder on your computer
2. Double-click "Start Cash Register Pro.bat" to launch the application
3. The application will start both the backend server and frontend interface

## Default Login Credentials

- Admin: username=admin, password=admin123
- Cashier: username=cashier, password=cashier123

## Features

- Complete point-of-sale system
- Table management
- Menu and category management
- Order processing
- Sales reporting
- User management (admin only)
- Thermal printer support

## System Requirements

- Windows 10 or later
- Python 3.7 or later (included)
- 4GB RAM minimum
- 1GB free disk space

## Support

For support and updates, please contact your system administrator.
'''
            
            readme_path = final_dist / "README.txt"
            with open(readme_path, 'w') as f:
                f.write(readme_content)
            
            print("✅ Final package created")
            print(f"📦 Application built in: {final_dist}")
            return True
            
        except Exception as e:
            print(f"❌ Error creating final package: {e}")
            return False
    
    def build(self):
        """Main build process"""
        print("🚀 Starting Cash Register Pro Build Process")
        print(f"Build directory: {self.root_dir}")
        
        start_time = time.time()
        
        steps = [
            self.check_dependencies,
            self.install_frontend_dependencies,
            self.install_backend_dependencies,
            self.prepare_build_directory,
            self.build_electron_app,
            self.copy_backend_to_dist,
            self.create_launcher_script,
            self.create_final_package
        ]
        
        for i, step in enumerate(steps, 1):
            if not step():
                print(f"\n❌ Build failed at step {i}")
                return False
        
        build_time = time.time() - start_time
        
        print(f"\n{'='*60}")
        print("🎉 BUILD COMPLETED SUCCESSFULLY!")
        print(f"{'='*60}")
        print(f"⏱️  Build time: {build_time:.1f} seconds")
        print(f"📦 Output directory: {self.dist_dir / 'Cash Register Pro'}")
        print(f"🚀 To run: Double-click 'Start Cash Register Pro.bat'")
        print(f"{'='*60}")
        
        return True

def main():
    """Main entry point"""
    builder = CashRegisterBuilder()
    success = builder.build()
    
    if not success:
        print("\n❌ Build failed!")
        input("Press Enter to exit...")
        sys.exit(1)
    else:
        print("\n✅ Build completed successfully!")
        input("Press Enter to exit...")
        sys.exit(0)

if __name__ == "__main__":
    main()
