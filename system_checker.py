#!/usr/bin/env python3
"""
Cash Register Pro - System Requirements Checker
Verifies all dependencies and system requirements are met
"""

import os
import sys
import subprocess
import platform
import shutil
import json
from pathlib import Path
import urllib.request
import zipfile
import tempfile

class SystemChecker:
    def __init__(self):
        self.requirements_met = True
        self.missing_components = []
        self.system_info = {
            'os': platform.system(),
            'os_version': platform.version(),
            'architecture': platform.architecture()[0],
            'python_version': platform.python_version(),
            'machine': platform.machine()
        }
        
    def print_header(self):
        """Print system checker header"""
        print("=" * 70)
        print("    CASH REGISTER PRO - SYSTEM REQUIREMENTS CHECKER")
        print("=" * 70)
        print()
        
    def print_system_info(self):
        """Print current system information"""
        print("📋 SYSTEM INFORMATION:")
        print(f"   Operating System: {self.system_info['os']} {self.system_info['os_version']}")
        print(f"   Architecture: {self.system_info['architecture']}")
        print(f"   Python Version: {self.system_info['python_version']}")
        print(f"   Machine Type: {self.system_info['machine']}")
        print()
        
    def check_operating_system(self):
        """Check if operating system is supported"""
        print("🖥️  Checking Operating System...")
        
        if self.system_info['os'] != 'Windows':
            print(f"   ❌ Unsupported OS: {self.system_info['os']}")
            print("   ℹ️  This application requires Windows 10 or 11")
            self.requirements_met = False
            self.missing_components.append("Windows Operating System")
            return False
            
        print(f"   ✅ Windows detected: {self.system_info['os_version']}")
        return True
        
    def check_python(self):
        """Check Python installation"""
        print("🐍 Checking Python Installation...")
        
        try:
            version = sys.version_info
            if version.major < 3 or (version.major == 3 and version.minor < 8):
                print(f"   ❌ Python version too old: {self.system_info['python_version']}")
                print("   ℹ️  Requires Python 3.8 or newer")
                self.requirements_met = False
                self.missing_components.append("Python 3.8+")
                return False
                
            print(f"   ✅ Python {self.system_info['python_version']} is compatible")
            return True
            
        except Exception as e:
            print(f"   ❌ Error checking Python: {e}")
            self.requirements_met = False
            self.missing_components.append("Python Installation")
            return False
            
    def check_node_npm(self):
        """Check Node.js and npm installation"""
        print("📦 Checking Node.js and npm...")
        
        # Check Node.js
        try:
            result = subprocess.run(['node', '--version'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                node_version = result.stdout.strip()
                print(f"   ✅ Node.js found: {node_version}")
                node_ok = True
            else:
                print("   ❌ Node.js not found or not working")
                node_ok = False
        except (subprocess.TimeoutExpired, FileNotFoundError):
            print("   ❌ Node.js not installed")
            node_ok = False
            
        # Check npm
        try:
            result = subprocess.run(['npm', '--version'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                npm_version = result.stdout.strip()
                print(f"   ✅ npm found: {npm_version}")
                npm_ok = True
            else:
                print("   ❌ npm not found or not working")
                npm_ok = False
        except (subprocess.TimeoutExpired, FileNotFoundError):
            print("   ❌ npm not installed")
            npm_ok = False
            
        if not node_ok:
            self.requirements_met = False
            self.missing_components.append("Node.js")
            
        if not npm_ok:
            self.requirements_met = False
            self.missing_components.append("npm")
            
        return node_ok and npm_ok
        
    def check_electron_app(self):
        """Check if Electron app is built and available"""
        print("⚡ Checking Electron Desktop Application...")
        
        frontend_dir = Path("frontend")
        if not frontend_dir.exists():
            print("   ❌ Frontend directory not found")
            self.requirements_met = False
            self.missing_components.append("Frontend Application")
            return False
            
        # Check for built Electron app
        possible_paths = [
            frontend_dir / "dist" / "win-unpacked" / "CashRegisterApp.exe",
            frontend_dir / "dist" / "win-unpacked" / "Cash Register Pro.exe",
            frontend_dir / "dist" / "CashRegisterApp.exe",
            frontend_dir / "dist" / "Cash Register Pro.exe"
        ]
        
        electron_found = False
        for path in possible_paths:
            if path.exists():
                print(f"   ✅ Electron app found: {path}")
                electron_found = True
                break
                
        if not electron_found:
            print("   ⚠️  Pre-built Electron app not found")
            print("   ℹ️  Will attempt to build during installation")
            
        # Check if we can build with npm
        package_json = frontend_dir / "package.json"
        if package_json.exists():
            print("   ✅ package.json found - can build Electron app")
            return True
        else:
            print("   ❌ package.json not found")
            self.requirements_met = False
            self.missing_components.append("Electron Configuration")
            return False
            
    def check_backend_dependencies(self):
        """Check backend Python dependencies"""
        print("🔧 Checking Backend Dependencies...")
        
        backend_dir = Path("backend")
        if not backend_dir.exists():
            print("   ❌ Backend directory not found")
            self.requirements_met = False
            self.missing_components.append("Backend Application")
            return False
            
        # Check for main backend file
        app_file = backend_dir / "app.py"
        if not app_file.exists():
            print("   ❌ Backend app.py not found")
            self.requirements_met = False
            self.missing_components.append("Backend Application")
            return False
            
        print("   ✅ Backend application found")
        
        # Check for requirements file
        requirements_file = backend_dir / "requirements.txt"
        if requirements_file.exists():
            print("   ✅ Requirements file found")
        else:
            print("   ⚠️  Requirements file not found")
            
        return True
        
    def check_disk_space(self):
        """Check available disk space"""
        print("💾 Checking Disk Space...")
        
        try:
            # Get current directory disk usage
            current_dir = Path.cwd()
            total, used, free = shutil.disk_usage(current_dir)
            
            free_mb = free // (1024 * 1024)
            required_mb = 500  # Require 500MB free space
            
            if free_mb >= required_mb:
                print(f"   ✅ Sufficient disk space: {free_mb}MB available")
                return True
            else:
                print(f"   ❌ Insufficient disk space: {free_mb}MB available, {required_mb}MB required")
                self.requirements_met = False
                self.missing_components.append(f"Disk Space ({required_mb}MB)")
                return False
                
        except Exception as e:
            print(f"   ⚠️  Could not check disk space: {e}")
            return True  # Don't fail on this check
            
    def check_network_connectivity(self):
        """Check internet connectivity for downloads"""
        print("🌐 Checking Network Connectivity...")
        
        try:
            # Try to connect to a reliable server
            urllib.request.urlopen('https://www.google.com', timeout=5)
            print("   ✅ Internet connection available")
            return True
        except:
            print("   ⚠️  No internet connection detected")
            print("   ℹ️  Internet required for initial setup only")
            return False
            
    def run_full_check(self):
        """Run all system checks"""
        self.print_header()
        self.print_system_info()
        
        print("🔍 RUNNING SYSTEM CHECKS...")
        print()
        
        # Run all checks
        checks = [
            self.check_operating_system,
            self.check_python,
            self.check_node_npm,
            self.check_electron_app,
            self.check_backend_dependencies,
            self.check_disk_space,
            self.check_network_connectivity
        ]
        
        for check in checks:
            check()
            print()
            
        # Print summary
        self.print_summary()
        
        return self.requirements_met
        
    def print_summary(self):
        """Print check summary"""
        print("=" * 70)
        print("    SYSTEM CHECK SUMMARY")
        print("=" * 70)
        
        if self.requirements_met:
            print("✅ ALL REQUIREMENTS MET!")
            print("   Your system is ready to run Cash Register Pro")
        else:
            print("❌ MISSING REQUIREMENTS DETECTED")
            print("   The following components need to be installed:")
            for component in self.missing_components:
                print(f"   • {component}")
            print()
            print("💡 Run the installer to automatically install missing components")
            
        print("=" * 70)

def main():
    """Main function"""
    checker = SystemChecker()
    
    if len(sys.argv) > 1 and sys.argv[1] == '--quiet':
        # Quiet mode - just return exit code
        return 0 if checker.run_full_check() else 1
    else:
        # Interactive mode
        success = checker.run_full_check()
        
        if not success:
            print()
            response = input("Would you like to run the installer now? (y/n): ")
            if response.lower() in ['y', 'yes']:
                print("Starting installer...")
                try:
                    subprocess.run([sys.executable, 'installer.py'], check=True)
                except FileNotFoundError:
                    print("❌ Installer not found. Please run installer.py manually.")
                except subprocess.CalledProcessError:
                    print("❌ Installer failed. Please check the error messages above.")
                    
        return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
