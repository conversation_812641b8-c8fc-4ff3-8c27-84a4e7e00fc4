#!/usr/bin/env python3
"""
Cash Register Pro - Single Executable Builder
Creates one .exe file that contains the complete application
"""

import os
import sys
import shutil
import subprocess
import tempfile
import zipfile
from pathlib import Path
import json

def install_pyinstaller():
    """Install PyInstaller if not available"""
    try:
        import PyInstaller
        print("[OK] PyInstaller is already installed")
        return True
    except ImportError:
        print("Installing PyInstaller...")
        try:
            subprocess.run([sys.executable, "-m", "pip", "install", "pyinstaller"], check=True)
            print("✓ PyInstaller installed successfully")
            return True
        except subprocess.CalledProcessError:
            print("✗ Failed to install PyInstaller")
            return False

def create_embedded_launcher():
    """Create the main launcher script with embedded resources"""
    launcher_code = '''
import os
import sys
import subprocess
import threading
import time
import tempfile
import zipfile
import shutil
from pathlib import Path
import webbrowser
import signal

class CashRegisterProLauncher:
    def __init__(self):
        self.backend_process = None
        self.temp_dir = None
        self.running = False
        
    def extract_resources(self):
        """Extract embedded resources"""
        if getattr(sys, 'frozen', False):
            # Running as executable
            bundle_dir = Path(sys._MEIPASS)
        else:
            # Running as script
            bundle_dir = Path(__file__).parent
            
        # Create temp directory
        self.temp_dir = Path(tempfile.mkdtemp(prefix="cashregister_"))
        
        # Extract backend
        backend_zip = bundle_dir / "backend.zip"
        if backend_zip.exists():
            with zipfile.ZipFile(backend_zip, 'r') as zip_ref:
                zip_ref.extractall(self.temp_dir)
        
        # Extract frontend
        frontend_zip = bundle_dir / "frontend.zip"
        if frontend_zip.exists():
            with zipfile.ZipFile(frontend_zip, 'r') as zip_ref:
                zip_ref.extractall(self.temp_dir)
                
        return self.temp_dir
    
    def start_backend(self):
        """Start Flask backend"""
        backend_dir = self.temp_dir / "backend"
        if not backend_dir.exists():
            print("❌ Backend files not found!")
            return False
            
        # Add to Python path
        sys.path.insert(0, str(backend_dir))
        
        try:
            os.chdir(backend_dir)
            from app import create_app
            app = create_app()
            
            def run_flask():
                app.run(host='127.0.0.1', port=5000, debug=False, use_reloader=False)
            
            backend_thread = threading.Thread(target=run_flask, daemon=True)
            backend_thread.start()
            
            print("✓ Backend server started on http://localhost:5000")
            return True
            
        except Exception as e:
            print(f"❌ Backend error: {e}")
            return False
    
    def start_frontend(self):
        """Start frontend"""
        # Wait for backend
        time.sleep(3)
        
        # Open in default browser
        try:
            webbrowser.open("http://localhost:5000")
            print("✓ Application opened in browser")
            return True
        except Exception as e:
            print(f"❌ Frontend error: {e}")
            return False
    
    def cleanup(self):
        """Cleanup temporary files"""
        if self.temp_dir and self.temp_dir.exists():
            try:
                shutil.rmtree(self.temp_dir)
            except:
                pass
    
    def signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        print("\\n🛑 Shutting down...")
        self.running = False
        self.cleanup()
        sys.exit(0)
    
    def run(self):
        """Main run method"""
        print("=" * 60)
        print("    💰 Cash Register Pro - Starting...")
        print("=" * 60)
        
        # Setup signal handlers
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
        
        try:
            # Extract resources
            print("📦 Extracting application files...")
            self.extract_resources()
            
            # Start backend
            print("🚀 Starting backend server...")
            if not self.start_backend():
                return False
            
            # Start frontend
            print("🌐 Opening application...")
            if not self.start_frontend():
                return False
            
            self.running = True
            
            print("\\n" + "=" * 60)
            print("    ✅ Cash Register Pro is now running!")
            print("=" * 60)
            print("\\n🌐 Application URL: http://localhost:5000")
            print("🔐 Default Credentials:")
            print("   👑 Admin: admin / admin123")
            print("   💼 Cashier: cashier / cashier123")
            print("\\n⌨️  Press Ctrl+C to stop")
            print("=" * 60)
            
            # Keep running
            try:
                while self.running:
                    time.sleep(1)
            except KeyboardInterrupt:
                pass
                
            return True
            
        except Exception as e:
            print(f"❌ Error: {e}")
            return False
        finally:
            self.cleanup()

if __name__ == "__main__":
    launcher = CashRegisterProLauncher()
    success = launcher.run()
    sys.exit(0 if success else 1)
'''
    
    with open("launcher_main.py", "w", encoding='utf-8') as f:
        f.write(launcher_code.strip())
    
    print("✓ Created embedded launcher script")

def create_resource_archives():
    """Create resource archives"""
    print("📦 Creating resource archives...")
    
    # Backend archive
    with zipfile.ZipFile("backend.zip", "w", zipfile.ZIP_DEFLATED) as zip_file:
        backend_path = Path("backend")
        for file_path in backend_path.rglob("*"):
            if file_path.is_file() and not file_path.name.endswith(('.pyc', '.pyo', '__pycache__')):
                arcname = file_path.relative_to(Path("."))
                zip_file.write(file_path, arcname)
                print(f"  📄 Added: {arcname}")
    
    # Frontend archive (HTML/CSS/JS files only)
    with zipfile.ZipFile("frontend.zip", "w", zipfile.ZIP_DEFLATED) as zip_file:
        frontend_path = Path("frontend")
        for file_path in frontend_path.rglob("*"):
            if (file_path.is_file() and 
                not file_path.name.startswith('.') and
                "node_modules" not in str(file_path) and
                "dist" not in str(file_path) and
                file_path.suffix in ['.html', '.css', '.js', '.json', '.md']):
                arcname = file_path.relative_to(Path("."))
                zip_file.write(file_path, arcname)
                print(f"  🌐 Added: {arcname}")
    
    print("✓ Resource archives created")

def build_executable():
    """Build the final executable"""
    print("🔨 Building executable...")
    
    # PyInstaller command
    cmd = [
        sys.executable, "-m", "PyInstaller",
        "--onefile",
        "--windowed",
        "--name", "CashRegisterPro",
        "--add-data", "backend.zip;.",
        "--add-data", "frontend.zip;.",
        "--hidden-import", "flask",
        "--hidden-import", "flask_sqlalchemy",
        "--hidden-import", "flask_cors",
        "--hidden-import", "jwt",
        "--hidden-import", "werkzeug",
        "--hidden-import", "sqlite3",
        "--hidden-import", "tempfile",
        "--hidden-import", "zipfile",
        "--hidden-import", "threading",
        "--hidden-import", "webbrowser",
        "--hidden-import", "signal",
        "launcher_main.py"
    ]
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✓ Executable built successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Build failed: {e}")
        print(f"Output: {e.stdout}")
        print(f"Error: {e.stderr}")
        return False

def main():
    """Main build process"""
    print("=" * 70)
    print("    💰 Cash Register Pro - Single Executable Builder")
    print("=" * 70)
    
    try:
        # Check requirements
        if not Path("backend").exists() or not Path("frontend").exists():
            print("❌ Backend or frontend directory not found!")
            return False
        
        # Install PyInstaller
        if not install_pyinstaller():
            return False
        
        # Create launcher
        create_embedded_launcher()
        
        # Create archives
        create_resource_archives()
        
        # Build executable
        if not build_executable():
            return False
        
        # Success message
        exe_path = Path("dist") / "CashRegisterPro.exe"
        if exe_path.exists():
            print("\\n" + "=" * 70)
            print("    🎉 BUILD SUCCESSFUL!")
            print("=" * 70)
            print(f"\\n📁 Executable created: {exe_path}")
            print(f"📏 File size: {exe_path.stat().st_size / 1024 / 1024:.1f} MB")
            print("\\n🚀 To run the application:")
            print("   1. Copy CashRegisterPro.exe to any computer")
            print("   2. Double-click to run")
            print("   3. Application will start automatically")
            print("\\n✨ Features included:")
            print("   ✅ Complete POS system")
            print("   ✅ User authentication")
            print("   ✅ Thermal printer support")
            print("   ✅ Inventory management")
            print("   ✅ Customer management")
            print("   ✅ Sales reporting")
            print("   ✅ Local database")
            print("   ✅ Modern web interface")
            return True
        else:
            print("❌ Executable not found after build")
            return False
            
    except Exception as e:
        print(f"❌ Build error: {e}")
        return False
    finally:
        # Cleanup
        for temp_file in ["launcher_main.py", "backend.zip", "frontend.zip"]:
            if Path(temp_file).exists():
                try:
                    os.remove(temp_file)
                except:
                    pass

if __name__ == "__main__":
    success = main()
    input("\\nPress Enter to exit...")
    sys.exit(0 if success else 1)
