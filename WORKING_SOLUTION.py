#!/usr/bin/env python3
"""
Cash Register Pro - Working Solution
Direct approach to start the application
"""

import os
import sys
import subprocess
import time
import webbrowser
from pathlib import Path

def main():
    print("=" * 60)
    print("💰 CASH REGISTER PRO - WORKING SOLUTION 💰")
    print("=" * 60)
    print()
    
    # Get directories
    root_dir = Path(__file__).parent
    backend_dir = root_dir / "backend"
    frontend_dir = root_dir / "frontend"
    
    # Clean database
    db_path = backend_dir / "cash_register.db"
    if db_path.exists():
        try:
            db_path.unlink()
            print("✅ Database reset for fresh schema")
        except:
            pass
    
    print("🚀 Starting backend server...")
    
    # Start backend in a new window
    backend_cmd = f'cd /d "{backend_dir}" && python -c "' + '''
import sys, os
sys.path.insert(0, os.getcwd())
from flask import Flask
from flask_cors import CORS
from api.auth import auth
from api.product import product
from api.category import category_bp
from api.order import order
from api.table import table
from api.sales import sales
from api.inventory import inventory
from api.customers import customers
from db.models import db, init_db
from config import Config

app = Flask(__name__)
app.config.from_object(Config)
db.init_app(app)
CORS(app)

app.register_blueprint(auth, url_prefix="/api/auth")
app.register_blueprint(product, url_prefix="/api")
app.register_blueprint(category_bp, url_prefix="/api")
app.register_blueprint(order, url_prefix="/api")
app.register_blueprint(table, url_prefix="/api")
app.register_blueprint(sales, url_prefix="/api")
app.register_blueprint(inventory, url_prefix="/api")
app.register_blueprint(customers, url_prefix="/api")

@app.route("/")
def home():
    return {"message": "Cash Register API Running"}

print("Initializing database...")
with app.app_context():
    init_db()
print("Database initialized!")
print("Backend server running on http://localhost:5000")
app.run(debug=False, host="0.0.0.0", port=5000, use_reloader=False)
''' + '"'
    
    # Start backend
    subprocess.Popen(['cmd', '/c', 'start', 'cmd', '/k', backend_cmd], shell=True)
    
    print("⏳ Waiting for backend to start...")
    time.sleep(5)
    
    print("🖥️  Starting frontend application...")
    
    # Start frontend
    frontend_cmd = f'cd /d "{frontend_dir}" && npm start'
    subprocess.Popen(['cmd', '/c', 'start', 'cmd', '/k', frontend_cmd], shell=True)
    
    print("⏳ Waiting for frontend to start...")
    time.sleep(3)
    
    print("\n" + "=" * 60)
    print("🎉 CASH REGISTER PRO IS NOW STARTING! 🎉")
    print("=" * 60)
    print()
    print("📍 Backend API: http://localhost:5000")
    print("🖥️  Frontend: Electron Desktop Application")
    print()
    print("🔐 DEFAULT LOGIN CREDENTIALS:")
    print("   👑 Admin: username=admin, password=admin123")
    print("   👤 Cashier: username=cashier, password=cashier123")
    print()
    print("✨ FEATURES INCLUDED:")
    print("   ✅ Complete Point-of-Sale System")
    print("   ✅ Table Management with Visual Cards")
    print("   ✅ Menu and Category Management")
    print("   ✅ Order Processing with Real-time Calculations")
    print("   ✅ Admin Dashboard for System Management")
    print("   ✅ Real-time Clock Display")
    print("   ✅ Professional UI/UX Design")
    print("   ✅ All Database Schema Issues Fixed")
    print("   ✅ Cache Errors Resolved")
    print("   ✅ Timestamps on Everything")
    print("   ✅ Local SQLite Database")
    print()
    print("🎯 WHAT YOU CAN DO:")
    print("   📋 Admin: Manage users, tables, menu items, view reports")
    print("   🛒 Cashier: Take orders, process payments, manage tables")
    print("   🪑 Tables: Click table cards to start taking orders")
    print("   🍽️  Menu: Add items by category with real-time totals")
    print("   💰 Orders: Automatic tax calculation (8.25%)")
    print("   🕐 Clock: Real-time display in header")
    print()
    print("📝 NOTES:")
    print("   • Two command windows will open (backend and frontend)")
    print("   • Keep both windows open while using the application")
    print("   • The Electron app window will appear shortly")
    print("   • Close the Electron window to stop the application")
    print()
    print("=" * 60)
    
    # Open browser as backup
    time.sleep(2)
    try:
        webbrowser.open('http://localhost:5000')
    except:
        pass
    
    input("Press Enter to exit this launcher...")

if __name__ == "__main__":
    main()
