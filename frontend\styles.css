/* CSS Variables for Theme Management */
:root {
  /* Enhanced Color Palette */
  --primary-color: #6366f1;
  --primary-hover: #4f46e5;
  --primary-light: #e0e7ff;
  --primary-dark: #3730a3;
  --secondary-color: #06b6d4;
  --secondary-hover: #0891b2;
  --secondary-light: #cffafe;
  --accent-color: #f59e0b;
  --accent-hover: #d97706;
  --accent-light: #fef3c7;
  --danger-color: #ef4444;
  --danger-hover: #dc2626;
  --danger-light: #fee2e2;
  --warning-color: #f59e0b;
  --warning-light: #fef3c7;
  --success-color: #10b981;
  --success-light: #d1fae5;
  --info-color: #3b82f6;
  --info-light: #dbeafe;

  /* Enhanced Background Colors */
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-tertiary: #f1f5f9;
  --bg-quaternary: #e2e8f0;
  --bg-dark: #1e293b;
  --bg-darker: #0f172a;
  --bg-glass: rgba(255, 255, 255, 0.1);
  --bg-glass-dark: rgba(0, 0, 0, 0.1);

  /* Enhanced Text Colors */
  --text-primary: #1e293b;
  --text-secondary: #64748b;
  --text-muted: #94a3b8;
  --text-light: #ffffff;
  --text-inverse: #ffffff;
  --text-accent: #6366f1;

  /* Enhanced Border Colors */
  --border-light: #e2e8f0;
  --border-medium: #cbd5e1;
  --border-dark: #475569;
  --border-focus: #6366f1;
  --border-error: #ef4444;

  /* Enhanced Shadows */
  --shadow-xs: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-sm: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px 0 rgb(0 0 0 / 0.06);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  --shadow-inner: inset 0 2px 4px 0 rgb(0 0 0 / 0.06);
  --shadow-glow: 0 0 20px rgb(99 102 241 / 0.3);
  --shadow-colored: 0 10px 25px -5px rgb(99 102 241 / 0.2);

  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;

  /* Border Radius */
  --radius-sm: 0.25rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;

  /* Enhanced Transitions */
  --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: 250ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 350ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-bounce: 500ms cubic-bezier(0.68, -0.55, 0.265, 1.55);
  --transition-spring: 300ms cubic-bezier(0.175, 0.885, 0.32, 1.275);

  /* Animation Durations */
  --duration-fast: 200ms;
  --duration-normal: 300ms;
  --duration-slow: 500ms;
}

/* Dark Theme */
[data-theme="dark"] {
  --bg-primary: #1e293b;
  --bg-secondary: #334155;
  --bg-tertiary: #475569;
  --text-primary: #f1f5f9;
  --text-secondary: #cbd5e1;
  --text-muted: #94a3b8;
  --border-light: #475569;
  --border-medium: #64748b;
}

/* Reset and Base Styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%);
  color: var(--text-primary);
  line-height: 1.6;
  min-height: 100vh;
  transition: all var(--transition-normal);
  overflow-x: hidden;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-tertiary);
  border-radius: var(--radius-md);
}

::-webkit-scrollbar-thumb {
  background: var(--border-medium);
  border-radius: var(--radius-md);
  transition: background var(--transition-fast);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--border-dark);
}

/* Button Styles */
button {
  cursor: pointer;
  padding: var(--spacing-sm) var(--spacing-md);
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
  color: var(--text-light);
  border: none;
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  font-weight: 500;
  transition: all var(--transition-fast);
  position: relative;
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left var(--transition-normal);
}

button:hover::before {
  left: 100%;
}

button:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
  background: linear-gradient(135deg, var(--primary-hover) 0%, var(--primary-color) 100%);
}

button:active {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

button:disabled {
  background: var(--border-medium);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

button:disabled::before {
  display: none;
}

/* Form Styles */
.form-group {
  margin-bottom: var(--spacing-lg);
  position: relative;
}

.form-group label {
  display: block;
  margin-bottom: var(--spacing-sm);
  font-weight: 600;
  color: var(--text-primary);
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: var(--spacing-md);
  border: 2px solid var(--border-light);
  border-radius: var(--radius-md);
  background: var(--bg-primary);
  color: var(--text-primary);
  font-size: 1rem;
  transition: all var(--transition-fast);
  outline: none;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
  transform: translateY(-1px);
}

.form-group input::placeholder {
  color: var(--text-muted);
  transition: opacity var(--transition-fast);
}

.form-group input:focus::placeholder {
  opacity: 0.7;
}

/* Window Controls */
.window-controls {
  position: fixed;
  top: 0;
  right: 0;
  z-index: 1000;
  display: flex;
  background: rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 0 0 0 var(--radius-md);
}

.window-control-btn {
  width: 46px;
  height: 32px;
  border: none;
  background: transparent;
  color: var(--text-primary);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background var(--transition-fast);
  font-size: 14px;
}

.window-control-btn:hover {
  background: rgba(255, 255, 255, 0.1);
}

.window-control-btn.close:hover {
  background: var(--danger-color);
  color: white;
}

/* Theme Toggle */
.theme-toggle {
  position: fixed;
  top: var(--spacing-md);
  left: var(--spacing-md);
  z-index: 1000;
  background: var(--bg-primary);
  border: 2px solid var(--border-light);
  border-radius: 50%;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--transition-fast);
  box-shadow: var(--shadow-md);
}

.theme-toggle:hover {
  transform: scale(1.1);
  box-shadow: var(--shadow-lg);
}

/* Enhanced Login Page */
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  padding: var(--spacing-xl);
  position: relative;
  background: linear-gradient(135deg,
    var(--primary-color) 0%,
    var(--secondary-color) 30%,
    var(--accent-color) 70%,
    var(--primary-dark) 100%);
  background-size: 400% 400%;
  animation: gradientShift 15s ease infinite;
}

@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

.login-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);
  opacity: 0.8;
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(1deg); }
}

/* Floating Elements */
.floating-elements {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 1;
}

.floating-element {
  position: absolute;
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.1), rgba(6, 182, 212, 0.1));
  border-radius: 50%;
  animation: floatAround var(--duration, 10s) linear infinite;
  animation-delay: var(--delay, 0s);
}

.floating-element:nth-child(1) {
  top: 20%;
  left: 10%;
  width: 40px;
  height: 40px;
}

.floating-element:nth-child(2) {
  top: 60%;
  right: 15%;
  width: 80px;
  height: 80px;
}

.floating-element:nth-child(3) {
  bottom: 30%;
  left: 20%;
  width: 50px;
  height: 50px;
}

.floating-element:nth-child(4) {
  top: 10%;
  right: 30%;
  width: 70px;
  height: 70px;
}

@keyframes floatAround {
  0% { transform: translate(0, 0) rotate(0deg); }
  25% { transform: translate(30px, -30px) rotate(90deg); }
  50% { transform: translate(-20px, -60px) rotate(180deg); }
  75% { transform: translate(-40px, -20px) rotate(270deg); }
  100% { transform: translate(0, 0) rotate(360deg); }
}

.login-form {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  padding: var(--spacing-2xl);
  border-radius: 24px;
  box-shadow:
    var(--shadow-2xl),
    0 0 0 1px rgba(255, 255, 255, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  width: 100%;
  max-width: 440px;
  position: relative;
  border: 1px solid rgba(255, 255, 255, 0.2);
  animation: slideInUp 0.8s var(--transition-bounce);
  overflow: hidden;
}

.login-form::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: conic-gradient(from 0deg, transparent, rgba(99, 102, 241, 0.1), transparent);
  animation: rotate 20s linear infinite;
  z-index: -1;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.login-form::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color), var(--accent-color));
  border-radius: var(--radius-xl) var(--radius-xl) 0 0;
}

/* Enhanced Login Header */
.login-header {
  text-align: center;
  margin-bottom: var(--spacing-2xl);
  position: relative;
}

.logo-container {
  position: relative;
  display: inline-block;
  margin-bottom: var(--spacing-lg);
}

.logo-icon {
  font-size: 4rem;
  position: relative;
  z-index: 2;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));
  animation: logoFloat 3s ease-in-out infinite;
}

@keyframes logoFloat {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.logo-rings {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1;
}

.ring {
  position: absolute;
  border: 2px solid;
  border-radius: 50%;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  opacity: 0.3;
}

.ring-1 {
  width: 80px;
  height: 80px;
  border-color: var(--primary-color);
  animation: ringPulse 2s ease-in-out infinite;
}

.ring-2 {
  width: 100px;
  height: 100px;
  border-color: var(--secondary-color);
  animation: ringPulse 2s ease-in-out infinite 0.5s;
}

.ring-3 {
  width: 120px;
  height: 120px;
  border-color: var(--accent-color);
  animation: ringPulse 2s ease-in-out infinite 1s;
}

@keyframes ringPulse {
  0%, 100% { transform: translate(-50%, -50%) scale(1); opacity: 0.3; }
  50% { transform: translate(-50%, -50%) scale(1.1); opacity: 0.1; }
}

.login-form h1 {
  margin: 0 0 var(--spacing-sm) 0;
  color: var(--text-primary);
  font-size: 2.5rem;
  font-weight: 800;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  letter-spacing: -0.02em;
}

.subtitle {
  color: var(--text-secondary);
  font-size: 1rem;
  font-weight: 500;
  margin: 0;
  opacity: 0.8;
}

.login-form .form-group {
  position: relative;
}

/* Enhanced Input Container */
.input-container {
  position: relative;
  overflow: visible;
  z-index: 1;
}

.login-form .form-group input {
  padding-left: 3.5rem;
  height: 4.5rem;
  font-size: 1.1rem;
  font-weight: 500;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(15px);
  border: 2px solid rgba(99, 102, 241, 0.1);
  transition: all var(--transition-spring);
  position: relative;
  z-index: 10;
  cursor: text;
  pointer-events: auto;
}

.input-focus-border {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
  transition: width var(--transition-spring);
  border-radius: 2px;
  z-index: 1;
  pointer-events: none;
}

.login-form .form-group input:focus {
  background: rgba(255, 255, 255, 0.95);
  border-color: var(--primary-color);
  box-shadow:
    0 0 0 4px rgba(99, 102, 241, 0.1),
    var(--shadow-glow);
  transform: translateY(-3px);
}

.login-form .form-group input:focus + .input-focus-border {
  width: 100%;
}

.login-form .form-group input:hover {
  border-color: rgba(99, 102, 241, 0.3);
  transform: translateY(-1px);
  background: rgba(255, 255, 255, 0.95);
}

.login-form .form-group::before {
  content: '';
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  width: 1.25rem;
  height: 1.25rem;
  background-size: contain;
  background-repeat: no-repeat;
  opacity: 0.6;
  z-index: 1;
}

.login-form .form-group:nth-child(2)::before {
  background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" /></svg>');
}

.login-form .form-group:nth-child(3)::before {
  background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" /></svg>');
}

#login-btn {
  width: 100%;
  height: 4rem;
  font-size: 1.2rem;
  font-weight: 700;
  margin-top: var(--spacing-xl);
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
  position: relative;
  overflow: hidden;
  border-radius: 16px;
  box-shadow: var(--shadow-colored);
  transition: all var(--transition-spring);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

#login-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
  transition: left var(--transition-normal);
}

#login-btn:hover {
  background: linear-gradient(135deg, var(--primary-hover) 0%, var(--secondary-hover) 100%);
  transform: translateY(-3px);
  box-shadow:
    var(--shadow-2xl),
    0 0 30px rgba(99, 102, 241, 0.4);
}

#login-btn:hover::before {
  left: 100%;
}

#login-btn:active {
  transform: translateY(-1px);
}

#login-btn:disabled {
  background: linear-gradient(135deg, #94a3b8 0%, #cbd5e1 100%);
  transform: none;
  box-shadow: var(--shadow-sm);
  cursor: not-allowed;
}

/* Button Ripple Effect */
.btn-ripple {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  pointer-events: none;
  border-radius: inherit;
}

.btn-ripple::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

#login-btn:active .btn-ripple::before {
  width: 300px;
  height: 300px;
}

/* Success Animation */
@keyframes successPulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.login-form.success {
  animation: successPulse 0.6s ease-in-out;
}

/* Enhanced Shake Animation */
@keyframes enhancedShake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-8px); }
  20%, 40%, 60%, 80% { transform: translateX(8px); }
}

.login-form.error {
  animation: enhancedShake 0.6s ease-in-out;
}

/* Particle Effects */
.particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  pointer-events: none;
  z-index: 0;
}

.particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: rgba(99, 102, 241, 0.6);
  border-radius: 50%;
  animation: particleFloat 15s linear infinite;
}

@keyframes particleFloat {
  0% {
    transform: translateY(100vh) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-100px) rotate(360deg);
    opacity: 0;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .login-container {
    padding: var(--spacing-md);
  }

  .login-form {
    padding: var(--spacing-xl);
    max-width: 100%;
  }

  .login-form h1 {
    font-size: 2rem;
  }

  .logo-icon {
    font-size: 3rem;
  }

  .floating-element {
    display: none;
  }
}

#error-message {
  color: var(--danger-color);
  margin-top: var(--spacing-lg);
  text-align: center;
  font-weight: 500;
  padding: var(--spacing-md);
  background: rgba(239, 68, 68, 0.1);
  border-radius: var(--radius-md);
  border-left: 4px solid var(--danger-color);
  animation: shake 0.5s ease-in-out;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

#error-message:empty {
  display: none;
}

/* Enhanced Demo Credentials */
.demo-credentials {
  margin-top: var(--spacing-xl);
  padding: var(--spacing-lg);
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.05) 0%, rgba(6, 182, 212, 0.05) 100%);
  border-radius: 16px;
  border: 1px solid rgba(99, 102, 241, 0.2);
  position: relative;
  overflow: hidden;
}

.demo-credentials::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(180deg, var(--primary-color), var(--secondary-color));
  border-radius: 0 4px 4px 0;
}

.demo-credentials h4 {
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.demo-credentials p {
  color: var(--text-secondary);
  font-size: 0.875rem;
  margin-bottom: var(--spacing-sm);
}

.demo-credentials strong {
  color: var(--text-primary);
}

/* Loading Overlay */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  backdrop-filter: blur(5px);
}

.loading-overlay p {
  color: white;
  margin-top: var(--spacing-md);
  font-weight: 500;
}

.loading-overlay .loading-spinner {
  width: 40px;
  height: 40px;
  border-width: 4px;
}

/* Notification System */
.notification-container {
  position: fixed;
  top: var(--spacing-xl);
  right: var(--spacing-xl);
  z-index: 10000;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  max-width: 400px;
}

.notification {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-xl);
  border-left: 4px solid var(--primary-color);
  animation: slideInRight 0.3s ease-out;
  position: relative;
  overflow: hidden;
}

.notification.success {
  border-left-color: var(--success-color);
}

.notification.error {
  border-left-color: var(--danger-color);
}

.notification.warning {
  border-left-color: var(--warning-color);
}

.notification::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  height: 3px;
  background: var(--primary-color);
  animation: notificationProgress 5s linear;
}

.notification.success::before {
  background: var(--success-color);
}

.notification.error::before {
  background: var(--danger-color);
}

.notification.warning::before {
  background: var(--warning-color);
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes notificationProgress {
  from { width: 100%; }
  to { width: 0%; }
}

.notification-title {
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.notification-message {
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.notification-close {
  position: absolute;
  top: var(--spacing-sm);
  right: var(--spacing-sm);
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  font-size: 1.25rem;
  padding: var(--spacing-xs);
  border-radius: 50%;
  transition: all var(--transition-fast);
}

.notification-close:hover {
  background: var(--bg-secondary);
  color: var(--text-primary);
}

/* Enhanced Loading Spinner */
.loading-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  border-top-color: white;
  border-right-color: rgba(255, 255, 255, 0.6);
  animation: spin 1s cubic-bezier(0.68, -0.55, 0.265, 1.55) infinite;
  position: relative;
}

.loading-spinner::after {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border: 1px solid transparent;
  border-top-color: rgba(255, 255, 255, 0.4);
  border-radius: 50%;
  animation: spin 2s linear infinite reverse;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Dashboard Layout */
.dashboard-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: var(--bg-secondary);
  position: relative;
}

/* Header Styles */
header {
  background: linear-gradient(135deg, var(--bg-dark) 0%, var(--bg-darker) 100%);
  color: var(--text-light);
  padding: var(--spacing-lg) var(--spacing-xl);
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: var(--shadow-lg);
  position: relative;
  z-index: 100;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color), var(--accent-color));
}

header h1 {
  margin: 0;
  font-size: 1.75rem;
  font-weight: 700;
  background: linear-gradient(135deg, #ffffff, #e2e8f0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

header h1::before {
  content: '💰';
  font-size: 2rem;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.header-center {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
}

.header-right {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

#user-info {
  background: rgba(255, 255, 255, 0.1);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-lg);
  font-weight: 500;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  background: rgba(16, 185, 129, 0.2);
  color: var(--success-color);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-lg);
  font-size: 0.875rem;
  font-weight: 500;
}

.status-indicator::before {
  content: '';
  width: 8px;
  height: 8px;
  background: var(--success-color);
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

#logout-btn {
  background: linear-gradient(135deg, var(--danger-color) 0%, var(--danger-hover) 100%);
  padding: var(--spacing-sm) var(--spacing-lg);
  font-weight: 600;
  border-radius: var(--radius-lg);
  transition: all var(--transition-fast);
}

#logout-btn:hover {
  background: linear-gradient(135deg, var(--danger-hover) 0%, var(--danger-color) 100%);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* Notification Badge */
.notification-badge {
  position: relative;
}

.notification-badge::after {
  content: attr(data-count);
  position: absolute;
  top: -8px;
  right: -8px;
  background: var(--danger-color);
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  font-weight: 600;
  animation: bounce 0.5s ease-in-out;
}

@keyframes bounce {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.2); }
}

/* Cashier Dashboard */
.dashboard-content {
  display: flex;
  flex: 1;
  overflow: hidden;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  background: var(--bg-secondary);
}

.section-card {
  background: var(--bg-primary);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  overflow: hidden;
  border: 1px solid var(--border-light);
  transition: all var(--transition-normal);
  position: relative;
}

.section-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

.section-header {
  padding: var(--spacing-lg) var(--spacing-xl);
  background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
  border-bottom: 1px solid var(--border-light);
  position: sticky;
  top: 0;
  z-index: 10;
}

.section-header h2 {
  margin: 0;
  color: var(--text-primary);
  font-size: 1.25rem;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.section-content {
  padding: var(--spacing-lg) var(--spacing-xl);
  overflow-y: auto;
  height: calc(100% - 80px);
}

.tables-section {
  flex: 1;
  min-width: 300px;
}

.tables-section .section-header h2::before {
  content: '🪑';
  font-size: 1.5rem;
}

.products-section {
  flex: 2;
  min-width: 400px;
}

.products-section .section-header h2::before {
  content: '🛍️';
  font-size: 1.5rem;
}

.order-section {
  flex: 1;
  min-width: 350px;
  border-left: 2px solid var(--border-light);
}

.order-section .section-header h2::before {
  content: '🧾';
  font-size: 1.5rem;
}

/* Search and Filter Bar */
.search-filter-bar {
  display: flex;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
  align-items: center;
  flex-wrap: wrap;
}

.search-input {
  flex: 1;
  min-width: 200px;
  position: relative;
}

.search-input input {
  padding-left: 3rem;
  height: 2.75rem;
  border-radius: var(--radius-lg);
  border: 2px solid var(--border-light);
  background: var(--bg-secondary);
}

.search-input::before {
  content: '🔍';
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  font-size: 1rem;
  z-index: 1;
}

.filter-dropdown {
  position: relative;
}

.filter-dropdown select {
  appearance: none;
  background: var(--bg-secondary);
  border: 2px solid var(--border-light);
  border-radius: var(--radius-lg);
  padding: var(--spacing-sm) var(--spacing-lg);
  padding-right: 2.5rem;
  cursor: pointer;
}

.filter-dropdown::after {
  content: '▼';
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  pointer-events: none;
  font-size: 0.75rem;
  color: var(--text-secondary);
}

/* Tables Grid */
#tables-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
  gap: var(--spacing-md);
  margin-top: var(--spacing-lg);
}

.table-card {
  background: var(--bg-primary);
  padding: var(--spacing-lg);
  border-radius: var(--radius-lg);
  text-align: center;
  cursor: pointer;
  box-shadow: var(--shadow-md);
  border: 2px solid var(--border-light);
  transition: all var(--transition-fast);
  position: relative;
  overflow: hidden;
  min-height: 120px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: var(--spacing-sm);
}

.table-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  transition: all var(--transition-fast);
}

.table-card.available::before {
  background: linear-gradient(90deg, var(--success-color), #34d399);
}

.table-card.occupied::before {
  background: linear-gradient(90deg, var(--danger-color), #f87171);
}

.table-card.reserved::before {
  background: linear-gradient(90deg, var(--warning-color), #fbbf24);
}

.table-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.table-card.selected {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.2);
  transform: translateY(-2px);
}

.table-number {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-primary);
}

.table-status {
  font-size: 0.875rem;
  font-weight: 500;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-md);
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.table-card.available .table-status {
  background: rgba(16, 185, 129, 0.1);
  color: var(--success-color);
}

.table-card.occupied .table-status {
  background: rgba(239, 68, 68, 0.1);
  color: var(--danger-color);
}

.table-card.reserved .table-status {
  background: rgba(245, 158, 11, 0.1);
  color: var(--warning-color);
}

.table-capacity {
  font-size: 0.75rem;
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.table-capacity::before {
  content: '👥';
  font-size: 0.875rem;
}

/* Category Filters */
.category-filters {
  display: flex;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-lg);
  flex-wrap: wrap;
  padding: var(--spacing-md);
  background: var(--bg-secondary);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-light);
}

.category-filter {
  padding: var(--spacing-sm) var(--spacing-md);
  background: var(--bg-primary);
  border: 2px solid var(--border-light);
  border-radius: var(--radius-lg);
  cursor: pointer;
  font-weight: 500;
  font-size: 0.875rem;
  transition: all var(--transition-fast);
  position: relative;
  overflow: hidden;
  white-space: nowrap;
}

.category-filter::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left var(--transition-normal);
}

.category-filter:hover::before {
  left: 100%;
}

.category-filter:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.category-filter.active {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
  color: var(--text-light);
  border-color: var(--primary-color);
  box-shadow: var(--shadow-md);
}

.category-filter.active::before {
  display: none;
}

/* Products Grid */
#products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  gap: var(--spacing-lg);
  margin-top: var(--spacing-lg);
}

.product-card {
  background: var(--bg-primary);
  padding: var(--spacing-lg);
  border-radius: var(--radius-lg);
  text-align: center;
  cursor: pointer;
  box-shadow: var(--shadow-md);
  border: 2px solid var(--border-light);
  transition: all var(--transition-fast);
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  min-height: 160px;
}

.product-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--secondary-color), var(--accent-color));
  transform: scaleX(0);
  transition: transform var(--transition-fast);
}

.product-card:hover::before {
  transform: scaleX(1);
}

.product-card:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-lg);
  border-color: var(--primary-color);
}

.product-card:active {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.product-image {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, var(--primary-light), var(--bg-secondary));
  border-radius: 50%;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  margin-bottom: var(--spacing-sm);
}

.product-name {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 1rem;
  margin-bottom: var(--spacing-xs);
}

.product-category {
  font-size: 0.75rem;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.025em;
  margin-bottom: var(--spacing-sm);
}

.product-price {
  font-weight: 700;
  color: var(--secondary-color);
  font-size: 1.25rem;
  margin-top: auto;
}

.product-card.out-of-stock {
  opacity: 0.6;
  cursor: not-allowed;
}

.product-card.out-of-stock::after {
  content: 'Out of Stock';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: var(--danger-color);
  color: white;
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-md);
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

/* Order Section Styles */
.selected-table-info {
  background: linear-gradient(135deg, var(--primary-light), var(--bg-secondary));
  border: 2px solid var(--primary-color);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
  text-align: center;
  transition: all var(--transition-fast);
}

.table-info-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-md);
}

.table-icon {
  font-size: 1.5rem;
}

.table-text {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 1.1rem;
}

.order-summary {
  margin-bottom: var(--spacing-lg);
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
  padding-bottom: var(--spacing-sm);
  border-bottom: 2px solid var(--border-light);
}

.order-header h3 {
  margin: 0;
  color: var(--text-primary);
  font-size: 1.1rem;
  font-weight: 600;
}

.clear-btn {
  background: var(--danger-color);
  color: white;
  border: none;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-md);
  font-size: 0.75rem;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.clear-btn:hover {
  background: var(--danger-hover);
  transform: translateY(-1px);
}

.clear-btn:disabled {
  background: var(--border-medium);
  cursor: not-allowed;
  transform: none;
}

.order-items-list {
  max-height: 300px;
  overflow-y: auto;
  margin-bottom: var(--spacing-lg);
}

.order-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-light);
  transition: background var(--transition-fast);
  border-radius: var(--radius-md);
  margin-bottom: var(--spacing-sm);
  background: var(--bg-secondary);
}

.order-item:hover {
  background: var(--bg-tertiary);
}

.item-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.item-name {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 0.95rem;
}

.item-price {
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.item-quantity {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-left: var(--spacing-md);
}

.quantity-btn {
  width: 32px;
  height: 32px;
  display: flex;
  justify-content: center;
  align-items: center;
  background: var(--bg-primary);
  border: 2px solid var(--border-light);
  border-radius: 50%;
  font-weight: 600;
  color: var(--text-primary);
  cursor: pointer;
  transition: all var(--transition-fast);
  font-size: 0.875rem;
}

.quantity-btn:hover {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
  transform: scale(1.1);
}

.quantity-display {
  min-width: 40px;
  text-align: center;
  font-weight: 600;
  color: var(--text-primary);
  background: var(--bg-primary);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-light);
}

.remove-item {
  color: var(--danger-color);
  cursor: pointer;
  margin-left: var(--spacing-md);
  padding: var(--spacing-xs);
  border-radius: 50%;
  transition: all var(--transition-fast);
  font-size: 1.25rem;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.remove-item:hover {
  background: rgba(239, 68, 68, 0.1);
  transform: scale(1.1);
}

/* Order Totals */
.order-totals {
  background: var(--bg-secondary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
  border: 1px solid var(--border-light);
}

.subtotal-row, .tax-row, .total-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-sm) 0;
}

.subtotal-row, .tax-row {
  color: var(--text-secondary);
  font-size: 0.95rem;
  border-bottom: 1px solid var(--border-light);
}

.total-row {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--text-primary);
  padding-top: var(--spacing-md);
  border-top: 2px solid var(--primary-color);
  margin-top: var(--spacing-sm);
}

/* Order Actions */
.order-actions {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}

.primary-btn, .secondary-btn, .tertiary-btn {
  width: 100%;
  height: 3rem;
  border: none;
  border-radius: var(--radius-lg);
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all var(--transition-fast);
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
}

.primary-btn {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
  color: white;
  box-shadow: var(--shadow-md);
}

.primary-btn:hover {
  background: linear-gradient(135deg, var(--primary-hover), var(--primary-color));
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.secondary-btn {
  background: linear-gradient(135deg, var(--secondary-color), var(--secondary-hover));
  color: white;
  box-shadow: var(--shadow-md);
}

.secondary-btn:hover {
  background: linear-gradient(135deg, var(--secondary-hover), var(--secondary-color));
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.tertiary-btn {
  background: var(--bg-primary);
  color: var(--text-primary);
  border: 2px solid var(--border-medium);
}

.tertiary-btn:hover {
  background: var(--bg-secondary);
  border-color: var(--primary-color);
  transform: translateY(-1px);
}

.primary-btn:disabled, .secondary-btn:disabled, .tertiary-btn:disabled {
  background: var(--border-medium);
  color: var(--text-muted);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Quick Actions */
.quick-actions {
  border-top: 2px solid var(--border-light);
  padding-top: var(--spacing-lg);
}

.quick-actions h4 {
  margin: 0 0 var(--spacing-md) 0;
  color: var(--text-primary);
  font-size: 0.95rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.quick-action-buttons {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
  gap: var(--spacing-sm);
}

.quick-action-btn {
  background: var(--bg-primary);
  border: 2px solid var(--border-light);
  border-radius: var(--radius-lg);
  padding: var(--spacing-md) var(--spacing-sm);
  cursor: pointer;
  transition: all var(--transition-fast);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-xs);
  text-align: center;
}

.quick-action-btn:hover {
  border-color: var(--primary-color);
  background: var(--primary-light);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.action-icon {
  font-size: 1.25rem;
}

.quick-action-btn span:last-child {
  font-size: 0.75rem;
  font-weight: 500;
  color: var(--text-secondary);
}

/* Empty States */
.empty-order {
  text-align: center;
  padding: var(--spacing-xl);
  color: var(--text-secondary);
}

.empty-order p {
  font-weight: 500;
  margin-bottom: var(--spacing-sm);
}

.empty-order small {
  font-size: 0.875rem;
  opacity: 0.8;
}

.no-products {
  grid-column: 1 / -1;
  text-align: center;
  padding: var(--spacing-xl);
  color: var(--text-secondary);
}

/* Modal Styles */
.modal {
  display: none;
  position: fixed;
  z-index: 10000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(5px);
}

.modal-content {
  background: var(--bg-primary);
  margin: 10% auto;
  padding: var(--spacing-xl);
  border-radius: var(--radius-xl);
  width: 90%;
  max-width: 500px;
  position: relative;
  box-shadow: var(--shadow-xl);
  border: 1px solid var(--border-light);
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.modal-content h3 {
  margin: 0 0 var(--spacing-lg) 0;
  color: var(--text-primary);
  font-size: 1.5rem;
  font-weight: 700;
}

.modal-actions {
  display: flex;
  gap: var(--spacing-md);
  margin-top: var(--spacing-xl);
}

.modal-actions button {
  flex: 1;
  height: 3rem;
}

/* Keyboard Shortcuts */
.keyboard-shortcuts {
  position: fixed;
  bottom: var(--spacing-xl);
  right: var(--spacing-xl);
  background: var(--bg-primary);
  border: 2px solid var(--border-light);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-xl);
  max-width: 300px;
  z-index: 1000;
}

.keyboard-shortcuts h4 {
  margin: 0 0 var(--spacing-md) 0;
  color: var(--text-primary);
  font-size: 1rem;
  font-weight: 600;
}

.shortcut-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.shortcut-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: 0.875rem;
  color: var(--text-secondary);
}

kbd {
  background: var(--bg-secondary);
  border: 1px solid var(--border-medium);
  border-radius: var(--radius-sm);
  padding: var(--spacing-xs) var(--spacing-sm);
  font-family: monospace;
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--text-primary);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Responsive Design */
@media (max-width: 1200px) {
  .dashboard-content {
    flex-direction: column;
    gap: var(--spacing-md);
  }

  .tables-section, .products-section, .order-section {
    flex: none;
    min-width: auto;
  }

  .order-section {
    border-left: none;
    border-top: 2px solid var(--border-light);
  }
}

@media (max-width: 768px) {
  .header-center, .header-right {
    display: none;
  }

  header h1 {
    font-size: 1.25rem;
  }

  .search-filter-bar {
    flex-direction: column;
    align-items: stretch;
  }

  .search-input {
    min-width: auto;
  }

  #tables-grid {
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  }

  #products-grid {
    grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
  }

  .order-actions {
    gap: var(--spacing-sm);
  }

  .quick-action-buttons {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Print Styles */
@media print {
  .window-controls,
  .theme-toggle,
  .notification-container,
  .keyboard-shortcuts {
    display: none !important;
  }
}

/* Animation Classes */
.fade-in {
  animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.slide-in-right {
  animation: slideInRight 0.3s ease-out;
}

.slide-out-right {
  animation: slideOutRight 0.3s ease-in;
}

@keyframes slideOutRight {
  from {
    opacity: 1;
    transform: translateX(0);
  }
  to {
    opacity: 0;
    transform: translateX(100%);
  }
}

/* Focus States for Accessibility */
button:focus,
input:focus,
select:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  :root {
    --border-light: #000000;
    --border-medium: #000000;
    --text-secondary: #000000;
  }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Signup Page Specific Styles */
.field-hint {
  display: block;
  font-size: 0.75rem;
  color: var(--text-muted);
  margin-top: var(--spacing-xs);
  font-style: italic;
}

.checkbox-group {
  margin-bottom: var(--spacing-lg);
}

.checkbox-label {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-sm);
  cursor: pointer;
  font-size: 0.875rem;
  line-height: 1.4;
}

.checkbox-label input[type="checkbox"] {
  width: auto;
  margin: 0;
  transform: scale(1.2);
  accent-color: var(--primary-color);
}

.link {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 500;
  transition: color var(--transition-fast);
}

.link:hover {
  color: var(--primary-hover);
  text-decoration: underline;
}

.auth-switch {
  text-align: center;
  margin-top: var(--spacing-xl);
  padding-top: var(--spacing-lg);
  border-top: 1px solid var(--border-light);
}

.auth-switch p {
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.features-preview {
  margin-top: var(--spacing-xl);
  padding: var(--spacing-lg);
  background: var(--bg-secondary);
  border-radius: var(--radius-md);
  border-left: 4px solid var(--primary-color);
}

.features-preview h4 {
  margin: 0 0 var(--spacing-md) 0;
  color: var(--text-primary);
  font-size: 0.95rem;
  font-weight: 600;
}

.features-preview ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.features-preview li {
  color: var(--text-secondary);
  font-size: 0.875rem;
  margin-bottom: var(--spacing-xs);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

#success-message {
  color: var(--success-color);
  margin-top: var(--spacing-lg);
  text-align: center;
  font-weight: 500;
  padding: var(--spacing-md);
  background: rgba(16, 185, 129, 0.1);
  border-radius: var(--radius-md);
  border-left: 4px solid var(--success-color);
  animation: slideInUp 0.3s ease-out;
}

#success-message:empty {
  display: none;
}

/* Enhanced form validation styles */
.form-group input:valid {
  border-color: var(--success-color);
}

.form-group input:invalid:not(:placeholder-shown) {
  border-color: var(--danger-color);
}

.form-group select:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

/* Password strength indicator */
.password-strength {
  margin-top: var(--spacing-xs);
  height: 4px;
  background: var(--border-light);
  border-radius: 2px;
  overflow: hidden;
}

.password-strength-bar {
  height: 100%;
  transition: all var(--transition-fast);
  border-radius: 2px;
}

.password-strength-weak {
  width: 33%;
  background: var(--danger-color);
}

.password-strength-medium {
  width: 66%;
  background: var(--warning-color);
}

.password-strength-strong {
  width: 100%;
  background: var(--success-color);
}

#selected-table {
  padding: 10px;
  background-color: #e8f5e9;
  border-radius: 4px;
  margin-bottom: 15px;
  font-weight: bold;
}

#order-items {
  margin-bottom: 15px;
}

.order-item {
  display: flex;
  justify-content: space-between;
  padding: 10px;
  border-bottom: 1px solid #ddd;
}

.item-details {
  flex: 1;
}

.item-name {
  font-weight: bold;
}

.item-price {
  color: #666;
  font-size: 14px;
}

.item-quantity {
  display: flex;
  align-items: center;
  gap: 5px;
}

.quantity-btn {
  width: 25px;
  height: 25px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f1f1f1;
  border: none;
  border-radius: 50%;
  font-weight: bold;
  color: #333;
}

.remove-item {
  color: #f44336;
  cursor: pointer;
  margin-left: 10px;
}

#order-total {
  font-size: 18px;
  font-weight: bold;
  margin: 15px 0;
}

#place-order-btn, #print-receipt-btn {
  width: 100%;
  margin-bottom: 10px;
}

#print-receipt-btn {
  background-color: #2196F3;
}

#print-receipt-btn:hover {
  background-color: #0b7dda;
}

/* Admin Dashboard */
.admin-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.admin-sidebar {
  width: 200px;
  background-color: #333;
  padding: 20px 0;
}

.nav-btn {
  display: block;
  width: 100%;
  text-align: left;
  padding: 12px 20px;
  background-color: transparent;
  color: white;
  border: none;
  border-radius: 0;
  font-size: 16px;
}

.nav-btn:hover {
  background-color: #444;
}

.nav-btn.active {
  background-color: #4CAF50;
}

.admin-main {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

.admin-section {
  display: none;
}

.admin-section.active {
  display: block;
}

.admin-section h2 {
  margin-bottom: 20px;
  color: #333;
}

.admin-table-container {
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  overflow-x: auto;
}

table {
  width: 100%;
  border-collapse: collapse;
}

th, td {
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid #ddd;
}

th {
  background-color: #f8f8f8;
  font-weight: bold;
}

.action-btn {
  margin-right: 5px;
  padding: 5px 10px;
  font-size: 12px;
}

.edit-btn {
  background-color: #2196F3;
}

.delete-btn {
  background-color: #f44336;
}

.view-btn {
  background-color: #FFC107;
  color: #333;
}

/* Modal */
.modal {
  display: none;
  position: fixed;
  z-index: 1;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
  background-color: white;
  margin: 10% auto;
  padding: 20px;
  border-radius: 8px;
  width: 400px;
  max-width: 90%;
  position: relative;
}

.close-btn {
  position: absolute;
  top: 10px;
  right: 15px;
  font-size: 24px;
  cursor: pointer;
}

/* Sales Report */
.sales-filters {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-bottom: 20px;
  align-items: flex-end;
}

#sales-report {
  background-color: white;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.sales-summary {
  margin-bottom: 20px;
}

.sales-total {
  font-size: 24px;
  font-weight: bold;
  color: #4CAF50;
  margin-bottom: 10px;
}

.sales-date {
  color: #666;
  margin-bottom: 20px;
}

.sales-categories, .sales-products {
  margin-top: 20px;
}

.sales-category, .sales-product {
  display: flex;
  justify-content: space-between;
  padding: 10px;
  border-bottom: 1px solid #ddd;
}

.sales-chart {
  height: 300px;
  margin-top: 20px;
}