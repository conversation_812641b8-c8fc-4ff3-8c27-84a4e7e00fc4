#!/usr/bin/env python3
"""
Cash Register Pro - Complete System Installer
Downloads and installs all required dependencies and creates desktop shortcuts
"""

import os
import sys
import subprocess
import platform
import shutil
import json
import urllib.request
import zipfile
import tempfile
import winreg
from pathlib import Path
import time

class CashRegisterInstaller:
    def __init__(self):
        self.install_dir = Path.cwd()
        self.temp_dir = Path(tempfile.gettempdir()) / "cash_register_installer"
        self.downloads = {}
        self.installed_components = []
        
        # Download URLs
        self.download_urls = {
            'nodejs': 'https://nodejs.org/dist/v20.10.0/node-v20.10.0-x64.msi',
            'python': 'https://www.python.org/ftp/python/3.11.7/python-3.11.7-amd64.exe'
        }
        
    def print_header(self):
        """Print installer header"""
        print("=" * 70)
        print("    CASH REGISTER PRO - COMPLETE SYSTEM INSTALLER")
        print("=" * 70)
        print()
        print("This installer will:")
        print("• Check system requirements")
        print("• Download and install missing dependencies")
        print("• Build the desktop application")
        print("• Create desktop shortcuts")
        print("• Configure the application for optimal performance")
        print()
        
    def create_temp_directory(self):
        """Create temporary directory for downloads"""
        try:
            self.temp_dir.mkdir(parents=True, exist_ok=True)
            print(f"✅ Created temporary directory: {self.temp_dir}")
            return True
        except Exception as e:
            print(f"❌ Failed to create temp directory: {e}")
            return False
            
    def download_file(self, url, filename, description):
        """Download a file with progress indication"""
        print(f"📥 Downloading {description}...")
        
        try:
            file_path = self.temp_dir / filename
            
            def progress_hook(block_num, block_size, total_size):
                if total_size > 0:
                    percent = min(100, (block_num * block_size * 100) // total_size)
                    print(f"\r   Progress: {percent}%", end="", flush=True)
                    
            urllib.request.urlretrieve(url, file_path, progress_hook)
            print(f"\n   ✅ Downloaded: {filename}")
            return file_path
            
        except Exception as e:
            print(f"\n   ❌ Download failed: {e}")
            return None
            
    def install_nodejs(self):
        """Install Node.js if not present"""
        print("📦 Checking Node.js installation...")
        
        # Check if already installed
        try:
            result = subprocess.run(['node', '--version'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                print(f"   ✅ Node.js already installed: {result.stdout.strip()}")
                return True
        except:
            pass
            
        print("   Installing Node.js...")
        
        # Download Node.js installer
        installer_path = self.download_file(
            self.download_urls['nodejs'],
            'nodejs_installer.msi',
            'Node.js'
        )
        
        if not installer_path:
            return False
            
        try:
            # Run installer silently
            print("   🔧 Running Node.js installer...")
            result = subprocess.run([
                'msiexec', '/i', str(installer_path), 
                '/quiet', '/norestart'
            ], timeout=300)
            
            if result.returncode == 0:
                print("   ✅ Node.js installed successfully")
                self.installed_components.append("Node.js")
                return True
            else:
                print(f"   ❌ Node.js installation failed (exit code: {result.returncode})")
                return False
                
        except subprocess.TimeoutExpired:
            print("   ❌ Node.js installation timed out")
            return False
        except Exception as e:
            print(f"   ❌ Node.js installation error: {e}")
            return False
            
    def install_python_packages(self):
        """Install required Python packages"""
        print("🐍 Installing Python packages...")
        
        packages = [
            'flask',
            'flask-cors',
            'flask-sqlalchemy',
            'flask-jwt-extended',
            'requests',
            'python-dotenv',
            'werkzeug'
        ]
        
        try:
            for package in packages:
                print(f"   Installing {package}...")
                result = subprocess.run([
                    sys.executable, '-m', 'pip', 'install', package
                ], capture_output=True, text=True, timeout=120)
                
                if result.returncode == 0:
                    print(f"   ✅ {package} installed")
                else:
                    print(f"   ⚠️  {package} installation warning: {result.stderr}")
                    
            print("   ✅ Python packages installation completed")
            self.installed_components.append("Python Packages")
            return True
            
        except Exception as e:
            print(f"   ❌ Python packages installation failed: {e}")
            return False
            
    def setup_frontend_dependencies(self):
        """Install frontend dependencies and build Electron app"""
        print("⚡ Setting up frontend application...")
        
        frontend_dir = self.install_dir / "frontend"
        if not frontend_dir.exists():
            print("   ❌ Frontend directory not found")
            return False
            
        try:
            # Change to frontend directory
            original_cwd = os.getcwd()
            os.chdir(frontend_dir)
            
            # Install npm dependencies
            print("   📦 Installing npm dependencies...")
            result = subprocess.run(['npm', 'install'], 
                                  capture_output=True, text=True, timeout=300)
            
            if result.returncode != 0:
                print(f"   ❌ npm install failed: {result.stderr}")
                return False
                
            print("   ✅ npm dependencies installed")
            
            # Build Electron application
            print("   🔨 Building Electron application...")
            result = subprocess.run(['npm', 'run', 'dist:win'], 
                                  capture_output=True, text=True, timeout=600)
            
            if result.returncode == 0:
                print("   ✅ Electron application built successfully")
                self.installed_components.append("Desktop Application")
            else:
                print(f"   ⚠️  Electron build completed with warnings: {result.stderr}")
                # Try alternative build
                print("   🔄 Trying alternative build method...")
                result = subprocess.run(['npm', 'run', 'pack'], 
                                      capture_output=True, text=True, timeout=300)
                if result.returncode == 0:
                    print("   ✅ Alternative build successful")
                    self.installed_components.append("Desktop Application")
                else:
                    print("   ⚠️  Build completed with warnings, but app should work")
                    
            return True
            
        except subprocess.TimeoutExpired:
            print("   ❌ Frontend setup timed out")
            return False
        except Exception as e:
            print(f"   ❌ Frontend setup failed: {e}")
            return False
        finally:
            os.chdir(original_cwd)
            
    def create_desktop_shortcut(self):
        """Create desktop shortcut for the application"""
        print("🖥️  Creating desktop shortcut...")
        
        try:
            # Get desktop path
            desktop = Path.home() / "Desktop"
            if not desktop.exists():
                # Try alternative desktop location
                desktop = Path.home() / "OneDrive" / "Desktop"
                if not desktop.exists():
                    print("   ⚠️  Desktop folder not found")
                    return False
                    
            # Create shortcut file
            shortcut_path = desktop / "Cash Register Pro.lnk"
            target_path = self.install_dir / "CashRegisterPro.py"
            
            # Create Windows shortcut using PowerShell
            powershell_script = f'''
$WshShell = New-Object -comObject WScript.Shell
$Shortcut = $WshShell.CreateShortcut("{shortcut_path}")
$Shortcut.TargetPath = "python"
$Shortcut.Arguments = '"{target_path}"'
$Shortcut.WorkingDirectory = "{self.install_dir}"
$Shortcut.IconLocation = "python.exe,0"
$Shortcut.Description = "Cash Register Pro - Professional POS System"
$Shortcut.Save()
'''
            
            result = subprocess.run([
                'powershell', '-Command', powershell_script
            ], capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                print(f"   ✅ Desktop shortcut created: {shortcut_path}")
                self.installed_components.append("Desktop Shortcut")
                return True
            else:
                print(f"   ❌ Shortcut creation failed: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"   ❌ Desktop shortcut creation failed: {e}")
            return False
            
    def create_start_menu_shortcut(self):
        """Create Start Menu shortcut"""
        print("📋 Creating Start Menu shortcut...")
        
        try:
            # Get Start Menu programs path
            start_menu = Path.home() / "AppData" / "Roaming" / "Microsoft" / "Windows" / "Start Menu" / "Programs"
            app_folder = start_menu / "Cash Register Pro"
            app_folder.mkdir(exist_ok=True)
            
            shortcut_path = app_folder / "Cash Register Pro.lnk"
            target_path = self.install_dir / "CashRegisterPro.py"
            
            # Create Windows shortcut using PowerShell
            powershell_script = f'''
$WshShell = New-Object -comObject WScript.Shell
$Shortcut = $WshShell.CreateShortcut("{shortcut_path}")
$Shortcut.TargetPath = "python"
$Shortcut.Arguments = '"{target_path}"'
$Shortcut.WorkingDirectory = "{self.install_dir}"
$Shortcut.IconLocation = "python.exe,0"
$Shortcut.Description = "Cash Register Pro - Professional POS System"
$Shortcut.Save()
'''
            
            result = subprocess.run([
                'powershell', '-Command', powershell_script
            ], capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                print(f"   ✅ Start Menu shortcut created")
                self.installed_components.append("Start Menu Shortcut")
                return True
            else:
                print(f"   ❌ Start Menu shortcut creation failed: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"   ❌ Start Menu shortcut creation failed: {e}")
            return False
            
    def cleanup_temp_files(self):
        """Clean up temporary files"""
        print("🧹 Cleaning up temporary files...")
        
        try:
            if self.temp_dir.exists():
                shutil.rmtree(self.temp_dir)
                print("   ✅ Temporary files cleaned up")
            return True
        except Exception as e:
            print(f"   ⚠️  Could not clean up temp files: {e}")
            return True  # Don't fail installation for this
            
    def run_installation(self):
        """Run the complete installation process"""
        self.print_header()
        
        print("🚀 STARTING INSTALLATION...")
        print()
        
        # Create temp directory
        if not self.create_temp_directory():
            return False
            
        try:
            # Installation steps
            steps = [
                ("Installing Node.js", self.install_nodejs),
                ("Installing Python packages", self.install_python_packages),
                ("Setting up frontend application", self.setup_frontend_dependencies),
                ("Creating desktop shortcut", self.create_desktop_shortcut),
                ("Creating Start Menu shortcut", self.create_start_menu_shortcut),
            ]
            
            failed_steps = []
            
            for step_name, step_function in steps:
                print(f"📋 {step_name}...")
                if not step_function():
                    failed_steps.append(step_name)
                print()
                
            # Print installation summary
            self.print_installation_summary(failed_steps)
            
            return len(failed_steps) == 0
            
        finally:
            self.cleanup_temp_files()
            
    def print_installation_summary(self, failed_steps):
        """Print installation summary"""
        print("=" * 70)
        print("    INSTALLATION SUMMARY")
        print("=" * 70)
        
        if not failed_steps:
            print("🎉 INSTALLATION COMPLETED SUCCESSFULLY!")
            print()
            print("✅ Installed components:")
            for component in self.installed_components:
                print(f"   • {component}")
            print()
            print("🚀 You can now run Cash Register Pro by:")
            print("   • Double-clicking the desktop shortcut")
            print("   • Using the Start Menu shortcut")
            print("   • Running: python CashRegisterPro.py")
            
        else:
            print("⚠️  INSTALLATION COMPLETED WITH SOME ISSUES")
            print()
            print("✅ Successfully installed:")
            for component in self.installed_components:
                print(f"   • {component}")
            print()
            print("❌ Failed steps:")
            for step in failed_steps:
                print(f"   • {step}")
            print()
            print("💡 You may still be able to run the application manually")
            
        print("=" * 70)

def main():
    """Main function"""
    if platform.system() != 'Windows':
        print("❌ This installer is designed for Windows only")
        return 1
        
    installer = CashRegisterInstaller()
    
    print("Cash Register Pro Installer")
    print("This will install all required dependencies and set up the application.")
    print()
    
    response = input("Do you want to continue? (y/n): ")
    if response.lower() not in ['y', 'yes']:
        print("Installation cancelled.")
        return 0
        
    success = installer.run_installation()
    
    if success:
        print()
        input("Press Enter to exit...")
        return 0
    else:
        print()
        input("Press Enter to exit...")
        return 1

if __name__ == "__main__":
    sys.exit(main())
