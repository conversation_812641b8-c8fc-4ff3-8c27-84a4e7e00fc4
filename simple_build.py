#!/usr/bin/env python3
"""
Cash Register Pro - Simple Executable Builder
Creates one .exe file that contains the complete application
"""

import os
import sys
import subprocess
import zipfile
from pathlib import Path

def install_pyinstaller():
    """Install PyInstaller if not available"""
    try:
        import PyInstaller
        print("[OK] PyInstaller is already installed")
        return True
    except ImportError:
        print("Installing PyInstaller...")
        try:
            subprocess.run([sys.executable, "-m", "pip", "install", "pyinstaller"], check=True)
            print("[OK] PyInstaller installed successfully")
            return True
        except subprocess.CalledProcessError:
            print("[ERROR] Failed to install PyInstaller")
            return False

def create_launcher():
    """Create the main launcher script"""
    launcher_code = '''
import os
import sys
import subprocess
import threading
import time
import tempfile
import zipfile
import shutil
from pathlib import Path
import webbrowser

class CashRegisterProLauncher:
    def __init__(self):
        self.temp_dir = None
        
    def extract_resources(self):
        """Extract embedded resources"""
        if getattr(sys, 'frozen', False):
            bundle_dir = Path(sys._MEIPASS)
        else:
            bundle_dir = Path(__file__).parent
            
        self.temp_dir = Path(tempfile.mkdtemp(prefix="cashregister_"))
        
        backend_zip = bundle_dir / "backend.zip"
        if backend_zip.exists():
            with zipfile.ZipFile(backend_zip, 'r') as zip_ref:
                zip_ref.extractall(self.temp_dir)
        
        frontend_zip = bundle_dir / "frontend.zip"
        if frontend_zip.exists():
            with zipfile.ZipFile(frontend_zip, 'r') as zip_ref:
                zip_ref.extractall(self.temp_dir)
                
        return self.temp_dir
    
    def start_backend(self):
        """Start Flask backend"""
        backend_dir = self.temp_dir / "backend"
        if not backend_dir.exists():
            print("[ERROR] Backend files not found!")
            return False
            
        sys.path.insert(0, str(backend_dir))
        
        try:
            os.chdir(backend_dir)
            from app import create_app
            app = create_app()
            
            def run_flask():
                app.run(host='127.0.0.1', port=5000, debug=False, use_reloader=False)
            
            backend_thread = threading.Thread(target=run_flask, daemon=True)
            backend_thread.start()
            
            print("[OK] Backend server started on http://localhost:5000")
            return True
            
        except Exception as e:
            print(f"[ERROR] Backend error: {e}")
            return False
    
    def start_frontend(self):
        """Start frontend"""
        time.sleep(3)
        
        try:
            webbrowser.open("http://localhost:5000")
            print("[OK] Application opened in browser")
            return True
        except Exception as e:
            print(f"[ERROR] Frontend error: {e}")
            return False
    
    def cleanup(self):
        """Cleanup temporary files"""
        if self.temp_dir and self.temp_dir.exists():
            try:
                shutil.rmtree(self.temp_dir)
            except:
                pass
    
    def run(self):
        """Main run method"""
        print("=" * 60)
        print("    Cash Register Pro - Starting...")
        print("=" * 60)
        
        try:
            print("Extracting application files...")
            self.extract_resources()
            
            print("Starting backend server...")
            if not self.start_backend():
                return False
            
            print("Opening application...")
            if not self.start_frontend():
                return False
            
            print("")
            print("=" * 60)
            print("    Cash Register Pro is now running!")
            print("=" * 60)
            print("")
            print("Application URL: http://localhost:5000")
            print("Default Credentials:")
            print("   Admin: admin / admin123")
            print("   Cashier: cashier / cashier123")
            print("")
            print("Press Ctrl+C to stop")
            print("=" * 60)
            
            try:
                while True:
                    time.sleep(1)
            except KeyboardInterrupt:
                print("\\nShutting down...")
                
            return True
            
        except Exception as e:
            print(f"[ERROR] Error: {e}")
            return False
        finally:
            self.cleanup()

if __name__ == "__main__":
    launcher = CashRegisterProLauncher()
    success = launcher.run()
    sys.exit(0 if success else 1)
'''
    
    with open("launcher_main.py", "w", encoding='utf-8') as f:
        f.write(launcher_code.strip())
    
    print("[OK] Created launcher script")

def create_archives():
    """Create resource archives"""
    print("Creating resource archives...")
    
    # Backend archive
    with zipfile.ZipFile("backend.zip", "w", zipfile.ZIP_DEFLATED) as zip_file:
        backend_path = Path("backend")
        for file_path in backend_path.rglob("*"):
            if file_path.is_file() and not file_path.name.endswith(('.pyc', '.pyo')):
                if '__pycache__' not in str(file_path):
                    arcname = file_path.relative_to(Path("."))
                    zip_file.write(file_path, arcname)
    
    # Frontend archive
    with zipfile.ZipFile("frontend.zip", "w", zipfile.ZIP_DEFLATED) as zip_file:
        frontend_path = Path("frontend")
        for file_path in frontend_path.rglob("*"):
            if (file_path.is_file() and 
                not file_path.name.startswith('.') and
                "node_modules" not in str(file_path) and
                "dist" not in str(file_path) and
                file_path.suffix in ['.html', '.css', '.js', '.json']):
                arcname = file_path.relative_to(Path("."))
                zip_file.write(file_path, arcname)
    
    print("[OK] Resource archives created")

def build_executable():
    """Build the final executable"""
    print("Building executable...")
    
    cmd = [
        sys.executable, "-m", "PyInstaller",
        "--onefile",
        "--windowed",
        "--name", "CashRegisterPro",
        "--add-data", "backend.zip;.",
        "--add-data", "frontend.zip;.",
        "--hidden-import", "flask",
        "--hidden-import", "flask_sqlalchemy",
        "--hidden-import", "flask_cors",
        "--hidden-import", "jwt",
        "--hidden-import", "werkzeug",
        "--hidden-import", "sqlite3",
        "launcher_main.py"
    ]
    
    try:
        subprocess.run(cmd, check=True)
        print("[OK] Executable built successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"[ERROR] Build failed: {e}")
        return False

def main():
    """Main build process"""
    print("=" * 60)
    print("    Cash Register Pro - Executable Builder")
    print("=" * 60)
    
    try:
        if not Path("backend").exists() or not Path("frontend").exists():
            print("[ERROR] Backend or frontend directory not found!")
            return False
        
        if not install_pyinstaller():
            return False
        
        create_launcher()
        create_archives()
        
        if not build_executable():
            return False
        
        exe_path = Path("dist") / "CashRegisterPro.exe"
        if exe_path.exists():
            print("")
            print("=" * 60)
            print("    BUILD SUCCESSFUL!")
            print("=" * 60)
            print(f"")
            print(f"Executable created: {exe_path}")
            print(f"File size: {exe_path.stat().st_size / 1024 / 1024:.1f} MB")
            print("")
            print("To run the application:")
            print("   1. Copy CashRegisterPro.exe to any computer")
            print("   2. Double-click to run")
            print("   3. Application will start automatically")
            return True
        else:
            print("[ERROR] Executable not found after build")
            return False
            
    except Exception as e:
        print(f"[ERROR] Build error: {e}")
        return False
    finally:
        for temp_file in ["launcher_main.py", "backend.zip", "frontend.zip"]:
            if Path(temp_file).exists():
                try:
                    os.remove(temp_file)
                except:
                    pass

if __name__ == "__main__":
    success = main()
    input("\nPress Enter to exit...")
    sys.exit(0 if success else 1)
