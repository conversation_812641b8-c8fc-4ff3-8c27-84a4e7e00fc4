document.addEventListener('DOMContentLoaded', () => {
  // Check if user is logged in
  const token = localStorage.getItem('authToken');
  const userRole = localStorage.getItem('userRole');
  const username = localStorage.getItem('username');
  
  if (!token) {
    window.location.href = 'index.html';
    return;
  }
  
  // Set up axios defaults
  axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
  
  // DOM elements
  const userNameElement = document.getElementById('user-name');
  const userRoleElement = document.getElementById('user-role');
  const logoutBtn = document.getElementById('logout-btn');
  const adminBtn = document.getElementById('admin-btn');
  const adminNavSection = document.getElementById('admin-nav-section');
  const navBtns = document.querySelectorAll('.nav-btn');
  const sections = document.querySelectorAll('.main-section');
  const sidebarToggle = document.getElementById('sidebar-toggle');
  const sidebar = document.getElementById('main-sidebar');
  
  // Current order state
  let currentOrder = {
    tableId: null,
    tableName: '',
    items: [],
    subtotal: 0,
    tax: 0,
    total: 0
  };
  
  // Initialize
  init();
  
  // Event listeners
  logoutBtn.addEventListener('click', logout);
  
  if (adminBtn) {
    adminBtn.addEventListener('click', () => {
      window.location.href = 'admin-dashboard.html';
    });
  }
  
  // Sidebar toggle functionality
  if (sidebarToggle && sidebar) {
    sidebarToggle.addEventListener('click', () => {
      sidebar.classList.toggle('collapsed');
    });
  }
  
  // Navigation
  navBtns.forEach(btn => {
    btn.addEventListener('click', () => {
      const section = btn.getAttribute('data-section');
      showSection(section);
      
      // Update active nav button
      navBtns.forEach(b => b.classList.remove('active'));
      btn.classList.add('active');
    });
  });
  
  // Window controls
  setupWindowControls();
  
  // Theme toggle
  setupThemeToggle();
  
  // Time update
  updateTime();
  setInterval(updateTime, 1000);
  
  // Form submissions
  setupFormHandlers();
  
  // Functions
  async function init() {
    try {
      // Set user info
      if (userNameElement) {
        userNameElement.textContent = username || 'User';
      }
      if (userRoleElement) {
        userRoleElement.textContent = userRole || 'Cashier';
      }
      
      // Show admin features if user is admin
      if (userRole === 'admin') {
        if (adminBtn) adminBtn.style.display = 'flex';
        if (adminNavSection) adminNavSection.style.display = 'block';
      }
      
      // Load initial data
      loadTables();
      loadCategories();
      
    } catch (error) {
      console.error('Initialization error:', error);
      alert('Failed to initialize dashboard. Please try again.');
    }
  }
  
  function setupWindowControls() {
    try {
      const { ipcRenderer } = require('electron');
      
      const minimizeBtn = document.getElementById('minimize-btn');
      const maximizeBtn = document.getElementById('maximize-btn');
      const closeBtn = document.getElementById('close-btn');
      
      if (minimizeBtn) {
        minimizeBtn.addEventListener('click', () => {
          ipcRenderer.invoke('window-minimize');
        });
      }
      
      if (maximizeBtn) {
        maximizeBtn.addEventListener('click', () => {
          ipcRenderer.invoke('window-maximize');
        });
      }
      
      if (closeBtn) {
        closeBtn.addEventListener('click', () => {
          window.close();
        });
      }
    } catch (error) {
      console.log('Window controls not available (running in browser)');
    }
  }
  
  function setupThemeToggle() {
    const themeToggle = document.getElementById('theme-toggle');
    const themeIcon = themeToggle?.querySelector('.theme-icon');
    
    if (themeToggle) {
      themeToggle.addEventListener('click', () => {
        const currentTheme = document.documentElement.getAttribute('data-theme');
        const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
        
        document.documentElement.setAttribute('data-theme', newTheme);
        localStorage.setItem('theme', newTheme);
        
        if (themeIcon) {
          themeIcon.textContent = newTheme === 'dark' ? '☀️' : '🌙';
        }
      });
      
      // Load saved theme
      const savedTheme = localStorage.getItem('theme') || 'light';
      document.documentElement.setAttribute('data-theme', savedTheme);
      if (themeIcon) {
        themeIcon.textContent = savedTheme === 'dark' ? '☀️' : '🌙';
      }
    }
  }
  
  function updateTime() {
    const now = new Date();
    const timeElement = document.getElementById('current-time');
    const dateElement = document.getElementById('current-date');
    
    if (timeElement) {
      timeElement.textContent = now.toLocaleTimeString([], { 
        hour: '2-digit', 
        minute: '2-digit',
        hour12: true 
      });
    }
    
    if (dateElement) {
      dateElement.textContent = now.toLocaleDateString([], { 
        weekday: 'long',
        month: 'short',
        day: 'numeric'
      });
    }
  }
  
  function showSection(sectionName) {
    sections.forEach(section => {
      section.classList.remove('active');
    });
    
    const targetSection = document.getElementById(`${sectionName}-section`);
    if (targetSection) {
      targetSection.classList.add('active');
      
      // Load section-specific data
      loadSectionData(sectionName);
    }
  }
  
  function loadSectionData(section) {
    switch (section) {
      case 'tables':
        loadTables();
        break;
      case 'orders':
        loadOrders();
        break;
      case 'menu':
        loadMenuItems();
        break;
      case 'add-table':
        // Form is already loaded
        break;
      case 'add-menu':
        loadCategoriesForForm();
        break;
    }
  }
  
  async function loadTables() {
    try {
      const response = await axios.get('http://localhost:5000/api/tables');
      const tables = response.data;
      
      const tablesGrid = document.getElementById('tables-grid');
      if (tablesGrid) {
        tablesGrid.innerHTML = tables.map(table => `
          <div class="table-card ${table.status}" data-table-id="${table.id}">
            <div class="table-header">
              <div class="table-number">${table.table_number || table.id}</div>
              <div class="table-status ${table.status}">${table.status}</div>
            </div>
            <div class="table-info">
              <div class="table-name">${table.name}</div>
              <div class="table-details">
                <div class="table-capacity">
                  <span>👥</span>
                  <span>${table.capacity || 4} seats</span>
                </div>
                <div class="table-location">
                  <span>📍</span>
                  <span>${table.location || 'Indoor'}</span>
                </div>
              </div>
            </div>
            <div class="table-actions">
              ${table.status === 'available' ? 
                `<button class="table-action-btn primary" onclick="openTableOrder(${table.id}, '${table.name}')">Take Order</button>` :
                `<button class="table-action-btn secondary" onclick="viewTableOrder(${table.id})">View Order</button>`
              }
            </div>
          </div>
        `).join('');
      }
      
      // Setup table filters
      setupTableFilters(tables);
      
    } catch (error) {
      console.error('Error loading tables:', error);
      // Create some default tables if none exist
      createDefaultTables();
    }
  }
  
  function setupTableFilters(tables) {
    const filterBtns = document.querySelectorAll('.filter-btn');
    
    filterBtns.forEach(btn => {
      btn.addEventListener('click', () => {
        const filter = btn.getAttribute('data-filter');
        
        // Update active filter button
        filterBtns.forEach(b => b.classList.remove('active'));
        btn.classList.add('active');
        
        // Filter tables
        const tableCards = document.querySelectorAll('.table-card');
        tableCards.forEach(card => {
          if (filter === 'all' || card.classList.contains(filter)) {
            card.style.display = 'block';
          } else {
            card.style.display = 'none';
          }
        });
      });
    });
  }
  
  async function createDefaultTables() {
    const defaultTables = [
      { table_number: 1, name: 'Table 1', capacity: 4, location: 'indoor', status: 'available' },
      { table_number: 2, name: 'Table 2', capacity: 2, location: 'indoor', status: 'available' },
      { table_number: 3, name: 'Window Table', capacity: 6, location: 'indoor', status: 'available' },
      { table_number: 4, name: 'Patio Table', capacity: 4, location: 'outdoor', status: 'available' }
    ];
    
    try {
      for (const table of defaultTables) {
        await axios.post('http://localhost:5000/api/tables', table);
      }
      loadTables();
    } catch (error) {
      console.error('Error creating default tables:', error);
    }
  }
  
  async function loadCategories() {
    try {
      const response = await axios.get('http://localhost:5000/api/categories');
      return response.data || [];
    } catch (error) {
      console.error('Error loading categories:', error);
      return [];
    }
  }
  
  async function loadMenuItems() {
    try {
      const response = await axios.get('http://localhost:5000/api/products');
      const menuItems = response.data;
      
      const menuItemsContainer = document.getElementById('menu-items');
      if (menuItemsContainer) {
        menuItemsContainer.innerHTML = menuItems.map(item => `
          <div class="menu-item-card">
            <div class="menu-item-info">
              <h5>${item.name}</h5>
              <p>${item.category}</p>
            </div>
            <div class="menu-item-price">$${item.price.toFixed(2)}</div>
          </div>
        `).join('');
      }
    } catch (error) {
      console.error('Error loading menu items:', error);
    }
  }
  
  async function loadOrders() {
    // Placeholder for orders functionality
    const ordersContainer = document.getElementById('orders-container');
    if (ordersContainer) {
      ordersContainer.innerHTML = '<p>No active orders</p>';
    }
  }
  
  function setupFormHandlers() {
    // Add table form
    const addTableForm = document.getElementById('add-table-form');
    if (addTableForm) {
      addTableForm.addEventListener('submit', async (e) => {
        e.preventDefault();
        
        const tableData = {
          table_number: parseInt(document.getElementById('new-table-number').value),
          name: document.getElementById('new-table-name').value,
          capacity: parseInt(document.getElementById('new-table-capacity').value),
          location: document.getElementById('new-table-location').value,
          status: 'available'
        };
        
        try {
          await axios.post('http://localhost:5000/api/tables', tableData);
          addTableForm.reset();
          loadTables();
          alert('Table added successfully!');
        } catch (error) {
          console.error('Error adding table:', error);
          alert('Failed to add table: ' + (error.response?.data?.error || error.message));
        }
      });
    }
    
    // Add menu item form
    const addMenuForm = document.getElementById('add-menu-form');
    if (addMenuForm) {
      addMenuForm.addEventListener('submit', async (e) => {
        e.preventDefault();
        
        const menuData = {
          name: document.getElementById('new-menu-name').value,
          description: document.getElementById('new-menu-description').value,
          category: document.getElementById('new-menu-category').value,
          price: parseFloat(document.getElementById('new-menu-price').value),
          available: document.getElementById('new-menu-available').checked
        };
        
        try {
          await axios.post('http://localhost:5000/api/products', menuData);
          addMenuForm.reset();
          loadMenuItems();
          alert('Menu item added successfully!');
        } catch (error) {
          console.error('Error adding menu item:', error);
          alert('Failed to add menu item: ' + (error.response?.data?.error || error.message));
        }
      });
    }
  }
  
  async function loadCategoriesForForm() {
    const categories = await loadCategories();
    const categorySelect = document.getElementById('new-menu-category');
    
    if (categorySelect) {
      categorySelect.innerHTML = '<option value="">Select category</option>' +
        categories.map(cat => `<option value="${cat.name}">${cat.name}</option>`).join('');
    }
  }
  
  function logout() {
    localStorage.removeItem('authToken');
    localStorage.removeItem('userRole');
    localStorage.removeItem('username');
    window.location.href = 'index.html';
  }
  
  // Global functions for table operations
  window.openTableOrder = function(tableId, tableName) {
    currentOrder.tableId = tableId;
    currentOrder.tableName = tableName;
    
    const modal = document.getElementById('table-order-modal');
    const title = document.getElementById('table-order-title');
    
    if (title) {
      title.textContent = `${tableName} - Order`;
    }
    
    // Load menu items for ordering
    loadMenuForOrdering();
    
    // Show modal
    if (modal) {
      modal.style.display = 'block';
    }
  };
  
  window.closeTableOrderModal = function() {
    const modal = document.getElementById('table-order-modal');
    if (modal) {
      modal.style.display = 'none';
    }
  };
  
  window.clearTableForm = function() {
    document.getElementById('add-table-form').reset();
  };
  
  window.clearMenuForm = function() {
    document.getElementById('add-menu-form').reset();
  };
  
  async function loadMenuForOrdering() {
    // This will be implemented in the next part
    console.log('Loading menu for ordering...');
  }
  
  function parseJwt(token) {
    try {
      return JSON.parse(atob(token.split('.')[1]));
    } catch (e) {
      return null;
    }
  }
});
