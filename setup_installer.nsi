;Cash Register Pro - NSIS Installer Script
;Creates a professional Windows installer package

!define APPNAME "Cash Register Pro"
!define COMPANYNAME "Cash Register Solutions"
!define DESCRIPTION "Professional Point of Sale System"
!define VERSIONMAJOR 2
!define VERSIONMINOR 0
!define VERSIONBUILD 0
!define HELPURL "https://github.com/yourusername/cash-register-app"
!define UPDATEURL "https://github.com/yourusername/cash-register-app/releases"
!define ABOUTURL "https://github.com/yourusername/cash-register-app"
!define INSTALLSIZE 500000 ; Size in KB

RequestExecutionLevel admin ;Require admin rights on NT6+ (When UAC is turned on)

InstallDir "$PROGRAMFILES64\${APPNAME}"

; rtf or txt file - remember if it is txt, it must be in the DOS text format (\r\n)
LicenseData "LICENSE.txt"
Name "${APPNAME}"
Icon "frontend\assets\icon.ico"
outFile "CashRegisterPro-Installer.exe"

!include LogicLib.nsh
!include WinVer.nsh

; Modern UI
!include "MUI2.nsh"

; MUI Settings
!define MUI_ABORTWARNING
!define MUI_ICON "frontend\assets\icon.ico"
!define MUI_UNICON "frontend\assets\icon.ico"

; Welcome page
!insertmacro MUI_PAGE_WELCOME

; License page
!insertmacro MUI_PAGE_LICENSE "LICENSE.txt"

; Components page
!insertmacro MUI_PAGE_COMPONENTS

; Directory page
!insertmacro MUI_PAGE_DIRECTORY

; Instfiles page
!insertmacro MUI_PAGE_INSTFILES

; Finish page
!define MUI_FINISHPAGE_RUN "$INSTDIR\CashRegisterPro.exe"
!define MUI_FINISHPAGE_RUN_TEXT "Launch Cash Register Pro"
!insertmacro MUI_PAGE_FINISH

; Uninstaller pages
!insertmacro MUI_UNPAGE_INSTFILES

; Language files
!insertmacro MUI_LANGUAGE "English"

; Version Information
VIProductVersion "${VERSIONMAJOR}.${VERSIONMINOR}.${VERSIONBUILD}.0"
VIAddVersionKey "ProductName" "${APPNAME}"
VIAddVersionKey "CompanyName" "${COMPANYNAME}"
VIAddVersionKey "LegalCopyright" "Copyright © 2024 ${COMPANYNAME}"
VIAddVersionKey "FileDescription" "${DESCRIPTION}"
VIAddVersionKey "FileVersion" "${VERSIONMAJOR}.${VERSIONMINOR}.${VERSIONBUILD}.0"
VIAddVersionKey "ProductVersion" "${VERSIONMAJOR}.${VERSIONMINOR}.${VERSIONBUILD}.0"

; Main installer section
Section "Cash Register Pro (required)" SecMain
    SectionIn RO
    
    ; Set output path to the installation directory
    SetOutPath $INSTDIR
    
    ; Copy main application files
    File "CashRegisterPro.py"
    File "desktop_launcher.py"
    File "system_checker.py"
    File "installer.py"
    File "requirements_downloader.py"
    File "*.md"
    File "*.txt"
    
    ; Copy backend directory
    SetOutPath "$INSTDIR\backend"
    File /r "backend\*.*"
    
    ; Copy frontend directory
    SetOutPath "$INSTDIR\frontend"
    File /r "frontend\*.*"
    
    ; Copy any other directories
    SetOutPath "$INSTDIR"
    File /nonfatal /r "printer"
    File /nonfatal /r "instance"
    
    ; Create launcher executable
    SetOutPath $INSTDIR
    File "CashRegisterPro.exe"
    
    ; Write the installation path into the registry
    WriteRegStr HKLM "SOFTWARE\${COMPANYNAME}\${APPNAME}" "InstallLocation" "$INSTDIR"
    
    ; Write the uninstall keys for Windows
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "DisplayName" "${APPNAME}"
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "UninstallString" "$\"$INSTDIR\uninstall.exe$\""
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "QuietUninstallString" "$\"$INSTDIR\uninstall.exe$\" /S"
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "InstallLocation" "$\"$INSTDIR$\""
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "DisplayIcon" "$\"$INSTDIR\frontend\assets\icon.ico$\""
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "Publisher" "${COMPANYNAME}"
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "HelpLink" "${HELPURL}"
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "URLUpdateInfo" "${UPDATEURL}"
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "URLInfoAbout" "${ABOUTURL}"
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "DisplayVersion" "${VERSIONMAJOR}.${VERSIONMINOR}.${VERSIONBUILD}"
    WriteRegDWORD HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "VersionMajor" ${VERSIONMAJOR}
    WriteRegDWORD HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "VersionMinor" ${VERSIONMINOR}
    WriteRegDWORD HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "NoModify" 1
    WriteRegDWORD HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "NoRepair" 1
    WriteRegDWORD HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "EstimatedSize" ${INSTALLSIZE}
    
    ; Create uninstaller
    WriteUninstaller "$INSTDIR\uninstall.exe"
SectionEnd

; Desktop shortcut section
Section "Desktop Shortcut" SecDesktop
    CreateShortCut "$DESKTOP\${APPNAME}.lnk" "$INSTDIR\CashRegisterPro.exe" "" "$INSTDIR\frontend\assets\icon.ico"
SectionEnd

; Start Menu shortcuts section
Section "Start Menu Shortcuts" SecStartMenu
    CreateDirectory "$SMPROGRAMS\${APPNAME}"
    CreateShortCut "$SMPROGRAMS\${APPNAME}\${APPNAME}.lnk" "$INSTDIR\CashRegisterPro.exe" "" "$INSTDIR\frontend\assets\icon.ico"
    CreateShortCut "$SMPROGRAMS\${APPNAME}\System Checker.lnk" "$INSTDIR\system_checker.py" "" 
    CreateShortCut "$SMPROGRAMS\${APPNAME}\Uninstall.lnk" "$INSTDIR\uninstall.exe"
SectionEnd

; Dependencies section
Section "Install Dependencies" SecDeps
    DetailPrint "Checking system requirements..."
    
    ; Check if Python is installed
    nsExec::ExecToStack 'python --version'
    Pop $0
    ${If} $0 != 0
        DetailPrint "Python not found, please install Python 3.8 or newer"
        MessageBox MB_OK "Python is required but not found. Please install Python 3.8 or newer and run the installer again."
    ${EndIf}
    
    ; Check if Node.js is installed
    nsExec::ExecToStack 'node --version'
    Pop $0
    ${If} $0 != 0
        DetailPrint "Node.js not found, attempting to install..."
        ; You could include Node.js installer here
        MessageBox MB_YESNO "Node.js is required but not found. Would you like to download and install it now?" IDYES InstallNodeJS IDNO SkipNodeJS
        InstallNodeJS:
            ExecShell "open" "https://nodejs.org/en/download/"
            MessageBox MB_OK "Please install Node.js from the opened webpage and then run this installer again."
            Abort
        SkipNodeJS:
    ${EndIf}
    
    ; Run Python installer to install dependencies
    DetailPrint "Installing Python dependencies..."
    nsExec::ExecToLog 'python "$INSTDIR\installer.py" --quiet'
    
    ; Build Electron app
    DetailPrint "Building desktop application..."
    SetOutPath "$INSTDIR\frontend"
    nsExec::ExecToLog 'npm install'
    nsExec::ExecToLog 'npm run dist:win'
SectionEnd

; Section descriptions
!insertmacro MUI_FUNCTION_DESCRIPTION_BEGIN
    !insertmacro MUI_DESCRIPTION_TEXT ${SecMain} "Core application files (required)"
    !insertmacro MUI_DESCRIPTION_TEXT ${SecDesktop} "Create a desktop shortcut"
    !insertmacro MUI_DESCRIPTION_TEXT ${SecStartMenu} "Create Start Menu shortcuts"
    !insertmacro MUI_DESCRIPTION_TEXT ${SecDeps} "Install and configure dependencies"
!insertmacro MUI_FUNCTION_DESCRIPTION_END

; Uninstaller section
Section "Uninstall"
    ; Remove registry keys
    DeleteRegKey HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}"
    DeleteRegKey HKLM "SOFTWARE\${COMPANYNAME}\${APPNAME}"
    
    ; Remove files and uninstaller
    Delete "$INSTDIR\uninstall.exe"
    Delete "$INSTDIR\*.*"
    
    ; Remove directories
    RMDir /r "$INSTDIR\backend"
    RMDir /r "$INSTDIR\frontend"
    RMDir /r "$INSTDIR\printer"
    RMDir /r "$INSTDIR\instance"
    RMDir "$INSTDIR"
    
    ; Remove shortcuts
    Delete "$DESKTOP\${APPNAME}.lnk"
    Delete "$SMPROGRAMS\${APPNAME}\*.*"
    RMDir "$SMPROGRAMS\${APPNAME}"
SectionEnd

; Functions
Function .onInit
    ; Check Windows version
    ${IfNot} ${AtLeastWin10}
        MessageBox MB_OK "This application requires Windows 10 or newer."
        Abort
    ${EndIf}
    
    ; Check if already installed
    ReadRegStr $R0 HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "UninstallString"
    StrCmp $R0 "" done
    
    MessageBox MB_OKCANCEL|MB_ICONEXCLAMATION "${APPNAME} is already installed. $\n$\nClick OK to remove the previous version or Cancel to cancel this upgrade." IDOK uninst
    Abort
    
    uninst:
        ClearErrors
        ExecWait '$R0 _?=$INSTDIR'
        
        IfErrors no_remove_uninstaller done
        no_remove_uninstaller:
    
    done:
FunctionEnd
