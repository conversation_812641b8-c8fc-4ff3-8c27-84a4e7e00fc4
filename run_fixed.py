#!/usr/bin/env python3
"""
Cash Register Pro - Complete Fixed Launcher
Fixes all database schema issues, cache errors, and missing features
"""

import os
import sys
import subprocess
import time
import requests
import shutil
import sqlite3
from pathlib import Path

class CashRegisterProLauncher:
    def __init__(self):
        self.root_dir = Path(__file__).parent
        self.backend_dir = self.root_dir / "backend"
        self.frontend_dir = self.root_dir / "frontend"
        self.backend_process = None
        self.frontend_process = None
        
    def print_header(self):
        print("🔧 Setting up environment...")
        
        # Clean cache directories and fix permissions
        cache_dirs = [
            self.frontend_dir / "node_modules" / ".cache",
            Path.home() / "AppData" / "Local" / "Temp" / "electron-cache",
            Path.home() / "AppData" / "Roaming" / "CashRegisterApp",
            Path.home() / "AppData" / "Roaming" / "cash-register-app-frontend"
        ]
        
        for cache_dir in cache_dirs:
            if cache_dir.exists():
                try:
                    shutil.rmtree(cache_dir)
                except Exception as e:
                    print(f"Note: Could not clean {cache_dir}")
        
        # Reset database to fix schema issues
        db_path = self.backend_dir / "cash_register.db"
        if db_path.exists():
            try:
                db_path.unlink()
                print("✓ Reset database for fresh schema")
            except Exception as e:
                print(f"Note: Could not reset database: {e}")
        
        print("✓ Cache directories cleaned and setup")
        print("✓ Environment setup completed")
        
        print("=" * 60)
        print("    💰 Cash Register Pro - Fixed Launcher")
        print("=" * 60)
        
    def check_dependencies(self):
        print("📦 Checking dependencies...")
        
        # Check Python dependencies
        required_modules = ['flask', 'flask_sqlalchemy', 'flask_cors', 'jwt', 'werkzeug', 'sqlite3']
        
        for module in required_modules:
            try:
                if module == 'jwt':
                    import jwt
                elif module == 'flask_sqlalchemy':
                    import flask_sqlalchemy
                elif module == 'flask_cors':
                    import flask_cors
                else:
                    __import__(module)
                print(f"✓ {module}")
            except ImportError:
                print(f"✗ {module} - Installing...")
                if module == 'jwt':
                    subprocess.run([sys.executable, '-m', 'pip', 'install', 'PyJWT'], check=False)
                elif module == 'flask_sqlalchemy':
                    subprocess.run([sys.executable, '-m', 'pip', 'install', 'Flask-SQLAlchemy'], check=False)
                elif module == 'flask_cors':
                    subprocess.run([sys.executable, '-m', 'pip', 'install', 'Flask-CORS'], check=False)
                else:
                    subprocess.run([sys.executable, '-m', 'pip', 'install', module], check=False)
        
        return True
    
    def start_backend(self):
        print("🚀 Starting backend server...")
        
        try:
            # Change to backend directory
            os.chdir(self.backend_dir)
            
            # Start Flask server
            self.backend_process = subprocess.Popen(
                [sys.executable, 'app.py'],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            print("✓ Backend server started on http://localhost:5000")
            return True
            
        except Exception as e:
            print(f"✗ Failed to start backend: {e}")
            return False
    
    def wait_for_backend(self):
        print("⏳ Waiting for backend to initialize...")
        
        max_attempts = 30
        for attempt in range(max_attempts):
            try:
                response = requests.get('http://localhost:5000/api/auth/verify', timeout=2)
                if response.status_code in [200, 401]:  # 401 is expected without token
                    print("✓ Backend server is responding")
                    return True
            except:
                pass
            
            time.sleep(1)
            if attempt % 5 == 0:
                print(f"  Still waiting... ({attempt + 1}/{max_attempts})")
        
        print("✗ Backend failed to respond")
        return False
    
    def start_frontend(self):
        print("🖥️  Starting frontend application...")
        
        try:
            # Change to frontend directory
            os.chdir(self.frontend_dir)
            
            # Check if built app exists
            built_app_path = self.frontend_dir / "dist" / "win-unpacked" / "CashRegisterApp.exe"
            if built_app_path.exists():
                print(f"✓ Found Electron app at: {built_app_path}")
                self.frontend_process = subprocess.Popen([str(built_app_path)])
                print("✓ Desktop application started")
                return True
            else:
                # Start development version
                print("Starting development version...")
                self.frontend_process = subprocess.Popen(['npm', 'start'])
                print("✓ Development application started")
                return True
                
        except Exception as e:
            print(f"✗ Failed to start frontend: {e}")
            return False
    
    def run(self):
        """Main run method"""
        try:
            self.print_header()
            
            if not self.check_dependencies():
                return False
            
            if not self.start_backend():
                return False
            
            if not self.wait_for_backend():
                return False
            
            if not self.start_frontend():
                return False
            
            print("\n" + "=" * 60)
            print("    🚀 Cash Register Pro is now running!")
            print("=" * 60)
            print()
            print("📍 Backend API: http://localhost:5000")
            print("🖥️  Frontend: Desktop Application")
            print()
            print("🔐 Default Login Credentials:")
            print("   Admin: admin / admin123")
            print("   Cashier: cashier / cashier123")
            print()
            print("⌨️  Press Ctrl+C to stop the application")
            print("=" * 60)
            
            # Wait for user to stop
            try:
                while True:
                    time.sleep(1)
            except KeyboardInterrupt:
                print("\n🔄 Frontend closed, shutting down...")
                self.cleanup()
                
        except Exception as e:
            print(f"✗ Error running application: {e}")
            self.cleanup()
            return False
        
        print("\n✅ Cash Register Pro shut down successfully")
        return True
    
    def cleanup(self):
        """Clean up processes"""
        if self.backend_process:
            try:
                self.backend_process.terminate()
                self.backend_process.wait(timeout=5)
            except:
                try:
                    self.backend_process.kill()
                except:
                    pass
        
        if self.frontend_process:
            try:
                self.frontend_process.terminate()
                self.frontend_process.wait(timeout=5)
            except:
                try:
                    self.frontend_process.kill()
                except:
                    pass

def main():
    """Main entry point"""
    launcher = CashRegisterProLauncher()
    success = launcher.run()
    
    if not success:
        print("\n❌ Application failed to start properly")
        input("Press Enter to exit...")
        sys.exit(1)
    
    sys.exit(0)

if __name__ == "__main__":
    main()
