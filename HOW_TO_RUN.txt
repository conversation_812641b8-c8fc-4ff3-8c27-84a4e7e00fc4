===============================================================================
                    HOW TO RUN CASH REGISTER PRO
                      Single Executable Application
===============================================================================

QUICK START - 3 SIMPLE STEPS
============================

STEP 1: LOCATE THE APPLICATION
------------------------------
Find the file: dist/CashRegisterPro.exe
Size: 41.9 MB
This is your complete Cash Register Pro application!

STEP 2: RUN THE APPLICATION
---------------------------
1. Copy CashRegisterPro.exe to any Windows computer
2. Double-click CashRegisterPro.exe
3. Wait 10-15 seconds for the application to start
4. Your web browser will open automatically
5. The application will be available at: http://localhost:5000

STEP 3: LOGIN AND START USING
-----------------------------
Use these default credentials:

ADMINISTRATOR:
- Username: admin
- Password: admin123
- Access: Full system control

CASHIER:
- Username: cashier
- Password: cashier123
- Access: Point-of-sale operations

OR CREATE NEW ACCOUNT:
- Click "Create Account" on the login page
- Fill in your details
- Start using immediately

===============================================================================

WHAT HAPPENS WHEN YOU RUN THE .EXE FILE
=======================================

1. EXTRACTION PHASE (5-10 seconds)
   - Application extracts temporary files
   - Sets up local database
   - Initializes backend server

2. STARTUP PHASE (3-5 seconds)
   - Flask backend server starts on port 5000
   - Database is created with sample data
   - Default users are created

3. LAUNCH PHASE (2-3 seconds)
   - Your default web browser opens
   - Application interface loads
   - Ready for login and use

4. READY TO USE!
   - Login with default credentials
   - Start processing orders immediately
   - All features are available

===============================================================================

COMPLETE FEATURE LIST
=====================

POINT-OF-SALE OPERATIONS:
- Table management with visual status
- Product catalog with categories
- Order processing with real-time totals
- Receipt printing (thermal and regular printers)
- Tax calculations (8.5% default)
- Discount application
- Multiple payment methods

USER MANAGEMENT:
- Secure login system
- User registration/signup
- Role-based access (Admin/Cashier)
- Password protection
- Session management

INVENTORY MANAGEMENT:
- Stock level tracking
- Low stock alerts
- Usage reports
- Reorder suggestions
- Category performance analytics

CUSTOMER MANAGEMENT:
- Customer database
- Loyalty points system
- Purchase history
- Contact information
- Rewards program

SALES REPORTING:
- Daily sales reports
- Product performance
- Revenue analytics
- Customer analytics
- Export capabilities

ADVANCED FEATURES:
- Dark/light theme switching
- Keyboard shortcuts
- Real-time notifications
- Search and filtering
- Professional UI/UX design

===============================================================================

THERMAL PRINTER SETUP
=====================

COMPATIBLE PRINTERS:
- ESC/POS thermal printers
- Standard Windows printers
- USB receipt printers
- Network printers

SETUP STEPS:
1. Connect your printer to the computer
2. Install printer drivers (if required)
3. Set the printer as default in Windows Settings
4. Test print from Windows to verify connection
5. Use the application's print test feature
6. Receipts will print automatically when orders are placed

TROUBLESHOOTING PRINTING:
- Ensure printer is powered on and connected
- Check printer is set as default in Windows
- Verify printer has paper and is ready
- Test print from another application first
- Try running the application as administrator

===============================================================================

SYSTEM REQUIREMENTS
===================

MINIMUM REQUIREMENTS:
- Windows 10 (64-bit) or Windows 11
- 4GB RAM
- 100MB free disk space
- Any modern web browser (Chrome, Firefox, Edge)

RECOMMENDED REQUIREMENTS:
- Windows 11
- 8GB RAM
- 500MB free disk space
- Google Chrome browser
- SSD storage for better performance

NETWORK REQUIREMENTS:
- No internet connection required
- Application runs completely offline
- All data stored locally

===============================================================================

TROUBLESHOOTING
===============

PROBLEM: Application won't start
SOLUTION: 
- Right-click CashRegisterPro.exe and "Run as administrator"
- Check Windows Defender isn't blocking the file
- Ensure you have enough free disk space (100MB+)
- Close other applications to free memory

PROBLEM: Browser doesn't open automatically
SOLUTION:
- Manually open your browser
- Go to: http://localhost:5000
- Bookmark this address for future use

PROBLEM: Login fails with default credentials
SOLUTION:
- Ensure you're typing: admin / admin123 (case sensitive)
- Try: cashier / cashier123
- Click "Create Account" to make a new user
- Check Caps Lock is off

PROBLEM: Port 5000 already in use
SOLUTION:
- Close other applications that might use port 5000
- Restart your computer
- Run the application as administrator

PROBLEM: Application runs slowly
SOLUTION:
- Close other applications to free memory
- Restart your computer
- Ensure you have adequate free disk space
- Run from SSD storage if available

===============================================================================

DATA AND SECURITY
=================

DATA STORAGE:
- All data stored locally on your computer
- No cloud storage or internet required
- Database automatically created and managed
- Data persists between application restarts

SECURITY FEATURES:
- Encrypted password storage
- Secure session management
- Role-based access control
- Local-only data (no external transmission)

BACKUP RECOMMENDATIONS:
- Copy CashRegisterPro.exe to backup location
- Data is embedded and automatically managed
- For enterprise use, consider regular system backups

===============================================================================

KEYBOARD SHORTCUTS
==================

GLOBAL SHORTCUTS:
- Ctrl + N: New order (clear current order)
- Ctrl + P: Print receipt
- Ctrl + D: Apply discount
- F1: Show/hide keyboard shortcuts help
- Esc: Clear current selection

NAVIGATION:
- Tab: Move between form fields
- Enter: Submit forms or confirm actions
- Space: Select buttons or checkboxes

===============================================================================

GETTING HELP
============

DOCUMENTATION:
- SUPPORT.txt: Complete troubleshooting guide
- USER_MANUAL.md: Detailed user instructions
- DEMO_GUIDE.md: Step-by-step demonstration
- FEATURES_SUMMARY.md: Complete feature list

COMMON SOLUTIONS:
1. Restart the application
2. Run as administrator
3. Check Windows Firewall settings
4. Ensure adequate system resources
5. Update Windows and drivers

===============================================================================

DEPLOYMENT ON MULTIPLE COMPUTERS
================================

SINGLE COMPUTER SETUP:
1. Copy CashRegisterPro.exe to the computer
2. Double-click to run
3. Use immediately

MULTIPLE COMPUTER SETUP:
1. Install on main computer (server)
2. Note the computer's IP address
3. Copy CashRegisterPro.exe to other computers
4. Configure network access (advanced users)
5. Ensure Windows Firewall allows port 5000

===============================================================================

SUCCESS INDICATORS
==================

APPLICATION IS WORKING CORRECTLY WHEN:
✓ CashRegisterPro.exe starts without errors
✓ Browser opens to http://localhost:5000
✓ Login page displays correctly
✓ You can login with default credentials
✓ Dashboard loads with tables and products
✓ You can create and place orders
✓ Receipts can be generated and printed

IF ALL ABOVE WORK: Your application is ready for production use!

===============================================================================

CONTACT AND SUPPORT
===================

For additional help:
1. Check all provided documentation files
2. Review the troubleshooting sections
3. Ensure system requirements are met
4. Try running as administrator
5. Restart computer if issues persist

Your Cash Register Pro application is complete and ready to use!

===============================================================================
                    ENJOY YOUR NEW CASH REGISTER PRO SYSTEM!
                          Professional POS Solution Ready!
===============================================================================
