===============================================================================
                    CASH REGISTER PRO - SUPPORT DOCUMENTATION
                          Single Executable Application
===============================================================================

OVERVIEW
========
Cash Register Pro is a complete point-of-sale (POS) system packaged as a single
executable file (CashRegisterPro.exe). This file contains everything needed to
run the application on any Windows computer without requiring any additional
software installation.

SYSTEM REQUIREMENTS
==================
- Operating System: Windows 10 or Windows 11 (64-bit)
- RAM: Minimum 4GB, Recommended 8GB
- Storage: 100MB free space
- Network: Not required (application runs offline)
- Printer: Any Windows-compatible printer (optional)

INSTALLATION & SETUP
====================

STEP 1: Download the Application
--------------------------------
- Locate the file: CashRegisterPro.exe (approximately 42MB)
- This is the complete application - no other files needed

STEP 2: Run the Application
---------------------------
1. Copy CashRegisterPro.exe to any location on your computer
2. Double-click CashRegisterPro.exe to start
3. Wait 10-15 seconds for the application to initialize
4. Your default web browser will open automatically
5. The application will display at: http://localhost:5000

STEP 3: First Time Login
------------------------
Use these default credentials to access the system:

ADMINISTRATOR ACCOUNT:
- Username: admin
- Password: admin123
- Access: Full system administration

CASHIER ACCOUNT:
- Username: cashier  
- Password: cashier123
- Access: Point-of-sale operations

IMPORTANT: Change these passwords after first login for security!

HOW IT WORKS
============
The executable contains:
- Complete backend server (Flask API)
- Database system (SQLite)
- Web-based user interface
- Thermal printer support
- All required dependencies

When you run CashRegisterPro.exe:
1. It extracts temporary files to your system
2. Starts a local web server on port 5000
3. Opens your browser to the application
4. Cleans up temporary files when closed

USING THE APPLICATION
====================

CREATING NEW USERS
------------------
1. Login as administrator (admin/admin123)
2. Click "Create Account" on login page, OR
3. Use admin dashboard to manage users

CASHIER OPERATIONS
-----------------
1. Login with cashier credentials
2. Select an available table (green indicator)
3. Browse products by category
4. Click products to add to order
5. Adjust quantities with +/- buttons
6. Click "Place Order" when ready
7. Print receipt for customer

ADMIN OPERATIONS
---------------
1. Login with admin credentials
2. Access admin dashboard for:
   - User management
   - Product management
   - Sales reports
   - Inventory tracking
   - System settings

KEYBOARD SHORTCUTS
=================
- Ctrl + N: New order (clear current)
- Ctrl + P: Print receipt
- Ctrl + D: Apply discount
- F1: Show/hide help
- Esc: Clear selection

THERMAL PRINTER SETUP
=====================
The application supports thermal printers and regular printers:

1. Connect your printer to the computer
2. Install printer drivers (if needed)
3. Set as default printer in Windows
4. Test printing from the application
5. Receipts will print automatically

Supported printer types:
- ESC/POS thermal printers
- Standard Windows printers
- Network printers
- USB receipt printers

TROUBLESHOOTING
==============

APPLICATION WON'T START
-----------------------
Problem: Double-clicking does nothing
Solution: 
- Right-click CashRegisterPro.exe → "Run as administrator"
- Check Windows Defender/antivirus isn't blocking
- Ensure you have sufficient disk space (100MB+)

BROWSER DOESN'T OPEN
-------------------
Problem: Application starts but no browser window
Solution:
- Manually open browser and go to: http://localhost:5000
- Check if another application is using port 5000
- Restart the application

LOGIN FAILS
-----------
Problem: Cannot login with default credentials
Solution:
- Ensure you're using correct credentials:
  admin/admin123 or cashier/cashier123
- Try creating a new account using "Sign Up"
- Check caps lock is off

PRINTER NOT WORKING
------------------
Problem: Receipts don't print
Solution:
- Verify printer is connected and powered on
- Check printer is set as default in Windows
- Test print from another application first
- Try the printer test function in the app

APPLICATION RUNS SLOWLY
-----------------------
Problem: Application is slow or unresponsive
Solution:
- Close other applications to free memory
- Restart CashRegisterPro.exe
- Check available disk space
- Restart your computer

PORT 5000 IN USE
----------------
Problem: Error about port 5000 being used
Solution:
- Close other applications that might use port 5000
- Restart your computer
- Run as administrator

DATA BACKUP
===========
Your data is automatically saved in a local database. To backup:

1. Close the application completely
2. Look for these files in your temp directory:
   - cash_register.db (main database)
3. Copy these files to a safe location
4. To restore: replace the files and restart

SECURITY NOTES
==============
- Change default passwords immediately
- The application runs locally (no internet required)
- Data stays on your computer
- Use Windows user accounts for additional security
- Regular backups recommended

ADVANCED FEATURES
================

INVENTORY MANAGEMENT
-------------------
- Track stock levels
- Low stock alerts
- Usage reports
- Reorder suggestions

CUSTOMER MANAGEMENT
------------------
- Customer database
- Loyalty program
- Purchase history
- Contact information

SALES REPORTING
--------------
- Daily sales reports
- Product performance
- Revenue analytics
- Export capabilities

MULTI-USER SUPPORT
------------------
- Multiple cashier accounts
- Role-based permissions
- User activity tracking
- Secure authentication

NETWORK DEPLOYMENT
==================
To run on multiple computers:

1. Install on main computer (server)
2. Note the computer's IP address
3. Copy CashRegisterPro.exe to other computers
4. Modify the application to use server IP
5. Ensure Windows Firewall allows port 5000

TECHNICAL SUPPORT
=================

COMMON ISSUES
------------
- Application crashes: Restart and run as administrator
- Slow performance: Close other programs, restart computer
- Printer issues: Check Windows printer settings
- Login problems: Use default credentials or create new account

GETTING HELP
-----------
1. Check this support document first
2. Try restarting the application
3. Run as administrator if problems persist
4. Check Windows Event Viewer for error details

SYSTEM INFORMATION
-----------------
- Application Version: 1.0
- Database: SQLite (embedded)
- Web Framework: Flask
- Frontend: HTML5/CSS3/JavaScript
- Packaging: PyInstaller

FILE LOCATIONS
=============
- Application: CashRegisterPro.exe (wherever you placed it)
- Temporary files: Windows temp directory (auto-cleaned)
- Database: Embedded in application
- Logs: Windows Event Viewer

UNINSTALLING
============
To remove the application:
1. Close CashRegisterPro.exe
2. Delete CashRegisterPro.exe file
3. Clear browser cache (optional)
4. No registry entries to clean

UPDATES
=======
To update the application:
1. Download new CashRegisterPro.exe
2. Close old version
3. Replace old file with new file
4. Your data will be preserved

PERFORMANCE TIPS
===============
- Close unnecessary applications
- Ensure adequate free disk space
- Use SSD storage for better performance
- Regular Windows updates
- Restart application daily for best performance

CONTACT INFORMATION
==================
For additional support or questions:
- Check the user manual (USER_MANUAL.md)
- Review the installation guide (INSTALLATION_GUIDE.md)
- Consult the demo guide (DEMO_GUIDE.md)

===============================================================================
                    Cash Register Pro - Your Complete POS Solution
                              Ready to Use Out of the Box!
===============================================================================
