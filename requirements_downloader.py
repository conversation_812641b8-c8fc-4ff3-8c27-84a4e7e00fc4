#!/usr/bin/env python3
"""
Cash Register Pro - Requirements Downloader
Downloads all necessary components for offline installation
"""

import os
import sys
import urllib.request
import json
import zipfile
import tempfile
from pathlib import Path
import hashlib

class RequirementsDownloader:
    def __init__(self):
        self.download_dir = Path("downloads")
        self.requirements = {
            'nodejs': {
                'url': 'https://nodejs.org/dist/v20.10.0/node-v20.10.0-x64.msi',
                'filename': 'nodejs-installer.msi',
                'description': 'Node.js Runtime',
                'size': '28MB',
                'required': True
            },
            'python': {
                'url': 'https://www.python.org/ftp/python/3.11.7/python-3.11.7-amd64.exe',
                'filename': 'python-installer.exe',
                'description': 'Python 3.11.7',
                'size': '25MB',
                'required': False  # Usually already installed
            },
            'electron_prebuilt': {
                'url': 'https://github.com/electron/electron/releases/download/v27.1.3/electron-v27.1.3-win32-x64.zip',
                'filename': 'electron-prebuilt.zip',
                'description': 'Electron Framework',
                'size': '150MB',
                'required': True
            },
            'vcredist': {
                'url': 'https://aka.ms/vs/17/release/vc_redist.x64.exe',
                'filename': 'vcredist_x64.exe',
                'description': 'Visual C++ Redistributable',
                'size': '25MB',
                'required': True
            }
        }
        
    def create_download_directory(self):
        """Create downloads directory"""
        try:
            self.download_dir.mkdir(exist_ok=True)
            print(f"✅ Download directory ready: {self.download_dir}")
            return True
        except Exception as e:
            print(f"❌ Failed to create download directory: {e}")
            return False
            
    def download_with_progress(self, url, filepath, description):
        """Download file with progress bar"""
        print(f"📥 Downloading {description}...")
        
        try:
            def progress_hook(block_num, block_size, total_size):
                if total_size > 0:
                    downloaded = block_num * block_size
                    percent = min(100, (downloaded * 100) // total_size)
                    mb_downloaded = downloaded / (1024 * 1024)
                    mb_total = total_size / (1024 * 1024)
                    
                    print(f"\r   Progress: {percent}% ({mb_downloaded:.1f}MB / {mb_total:.1f}MB)", 
                          end="", flush=True)
                    
            urllib.request.urlretrieve(url, filepath, progress_hook)
            print(f"\n   ✅ Downloaded: {filepath.name}")
            return True
            
        except Exception as e:
            print(f"\n   ❌ Download failed: {e}")
            return False
            
    def verify_file(self, filepath, expected_size_mb=None):
        """Verify downloaded file"""
        try:
            if not filepath.exists():
                return False
                
            size_mb = filepath.stat().st_size / (1024 * 1024)
            
            if expected_size_mb and abs(size_mb - expected_size_mb) > 5:  # 5MB tolerance
                print(f"   ⚠️  File size mismatch: {size_mb:.1f}MB (expected ~{expected_size_mb}MB)")
                
            print(f"   ✅ File verified: {size_mb:.1f}MB")
            return True
            
        except Exception as e:
            print(f"   ❌ File verification failed: {e}")
            return False
            
    def download_all_requirements(self):
        """Download all required components"""
        print("=" * 70)
        print("    CASH REGISTER PRO - REQUIREMENTS DOWNLOADER")
        print("=" * 70)
        print()
        print("This will download all components needed for offline installation:")
        
        total_size = 0
        for req in self.requirements.values():
            print(f"• {req['description']} ({req['size']})")
            # Extract size for calculation
            size_str = req['size'].replace('MB', '')
            total_size += int(size_str)
            
        print(f"\nTotal download size: ~{total_size}MB")
        print()
        
        response = input("Continue with download? (y/n): ")
        if response.lower() not in ['y', 'yes']:
            print("Download cancelled.")
            return False
            
        if not self.create_download_directory():
            return False
            
        print("\n🚀 Starting downloads...")
        print()
        
        downloaded_files = []
        failed_downloads = []
        
        for name, req in self.requirements.items():
            filepath = self.download_dir / req['filename']
            
            # Skip if already exists and verified
            if filepath.exists():
                print(f"📁 {req['description']} already exists, verifying...")
                if self.verify_file(filepath):
                    downloaded_files.append(name)
                    continue
                else:
                    print(f"   🔄 Re-downloading due to verification failure...")
                    
            # Download the file
            if self.download_with_progress(req['url'], filepath, req['description']):
                if self.verify_file(filepath):
                    downloaded_files.append(name)
                else:
                    failed_downloads.append(name)
            else:
                failed_downloads.append(name)
                
            print()
            
        # Create download summary
        self.create_download_summary(downloaded_files, failed_downloads)
        
        return len(failed_downloads) == 0
        
    def create_download_summary(self, downloaded, failed):
        """Create download summary file"""
        summary = {
            'download_date': str(Path().cwd()),
            'downloaded_files': downloaded,
            'failed_downloads': failed,
            'total_files': len(self.requirements),
            'success_rate': len(downloaded) / len(self.requirements) * 100
        }
        
        summary_file = self.download_dir / "download_summary.json"
        try:
            with open(summary_file, 'w') as f:
                json.dump(summary, f, indent=2)
            print(f"📋 Download summary saved: {summary_file}")
        except Exception as e:
            print(f"⚠️  Could not save summary: {e}")
            
        # Print summary
        print("=" * 70)
        print("    DOWNLOAD SUMMARY")
        print("=" * 70)
        
        if not failed:
            print("🎉 ALL DOWNLOADS COMPLETED SUCCESSFULLY!")
            print()
            print("✅ Downloaded components:")
            for name in downloaded:
                req = self.requirements[name]
                print(f"   • {req['description']}")
                
        else:
            print("⚠️  DOWNLOADS COMPLETED WITH SOME FAILURES")
            print()
            print("✅ Successfully downloaded:")
            for name in downloaded:
                req = self.requirements[name]
                print(f"   • {req['description']}")
                
            print()
            print("❌ Failed downloads:")
            for name in failed:
                req = self.requirements[name]
                print(f"   • {req['description']}")
                
        print()
        print(f"📁 All files saved to: {self.download_dir.absolute()}")
        print("💡 You can now run the installer offline using these files")
        print("=" * 70)
        
    def create_offline_installer_script(self):
        """Create offline installer script"""
        script_content = '''@echo off
echo ===============================================
echo    Cash Register Pro - Offline Installer
echo ===============================================
echo.

echo Installing Visual C++ Redistributable...
downloads\\vcredist_x64.exe /quiet /norestart
if %errorlevel% neq 0 echo Warning: VC++ Redistributable installation failed

echo.
echo Installing Node.js...
msiexec /i downloads\\nodejs-installer.msi /quiet /norestart
if %errorlevel% neq 0 echo Warning: Node.js installation failed

echo.
echo Setting up application...
python installer.py

echo.
echo Installation complete!
pause
'''
        
        script_file = Path("install_offline.bat")
        try:
            with open(script_file, 'w') as f:
                f.write(script_content)
            print(f"📜 Offline installer script created: {script_file}")
            return True
        except Exception as e:
            print(f"❌ Failed to create installer script: {e}")
            return False

def main():
    """Main function"""
    downloader = RequirementsDownloader()
    
    if len(sys.argv) > 1 and sys.argv[1] == '--script-only':
        # Just create the offline installer script
        return 0 if downloader.create_offline_installer_script() else 1
    
    # Download all requirements
    success = downloader.download_all_requirements()
    
    if success:
        # Create offline installer script
        downloader.create_offline_installer_script()
        
        print()
        input("Press Enter to exit...")
        return 0
    else:
        print()
        print("💡 You can retry failed downloads by running this script again")
        input("Press Enter to exit...")
        return 1

if __name__ == "__main__":
    sys.exit(main())
