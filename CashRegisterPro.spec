# -*- mode: python ; coding: utf-8 -*-


a = Analysis(
    ['launcher_main.py'],
    pathex=[],
    binaries=[],
    datas=[('backend.zip', '.'), ('frontend.zip', '.')],
    hiddenimports=['flask', 'flask_sqlalchemy', 'flask_cors', 'jwt', 'werkzeug', 'sqlite3'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='CashRegisterPro',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
