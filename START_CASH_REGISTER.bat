@echo off
title Cash Register Pro - Complete Application
color 0A
echo.
echo ████████████████████████████████████████████████████████████████
echo ██                                                            ██
echo ██        💰 CASH REGISTER PRO - PROFESSIONAL POS 💰         ██
echo ██                                                            ██
echo ████████████████████████████████████████████████████████████████
echo.
echo 🚀 Starting Complete Point-of-Sale System...
echo.

REM Kill any existing processes
echo 🔄 Cleaning up existing processes...
taskkill /f /im "CashRegisterApp.exe" 2>nul
taskkill /f /im "Cash Register Pro.exe" 2>nul
taskkill /f /im python.exe 2>nul
taskkill /f /im node.exe 2>nul

REM Clean cache directories to fix Electron errors
echo 🧹 Cleaning cache directories...
if exist "%USERPROFILE%\AppData\Local\Temp\electron-cache" (
    rmdir /s /q "%USERPROFILE%\AppData\Local\Temp\electron-cache" 2>nul
)
if exist "%USERPROFILE%\AppData\Roaming\CashRegisterApp" (
    rmdir /s /q "%USERPROFILE%\AppData\Roaming\CashRegisterApp" 2>nul
)
if exist "%USERPROFILE%\AppData\Roaming\cash-register-app-frontend" (
    rmdir /s /q "%USERPROFILE%\AppData\Roaming\cash-register-app-frontend" 2>nul
)

REM Reset database to fix schema issues
echo 🗄️  Resetting database for fresh schema...
if exist "backend\cash_register.db" (
    del "backend\cash_register.db" 2>nul
    echo ✅ Database reset successfully
)

REM Set environment variables to prevent Electron errors
set ELECTRON_DISABLE_SECURITY_WARNINGS=true
set ELECTRON_ENABLE_LOGGING=false
set ELECTRON_NO_ATTACH_CONSOLE=true

echo.
echo 📦 Installing/Checking Dependencies...

REM Install backend dependencies
echo 🐍 Installing Python dependencies...
cd backend
pip install Flask Flask-SQLAlchemy Flask-CORS PyJWT Werkzeug python-dotenv >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Failed to install Python dependencies
    pause
    exit /b 1
)
echo ✅ Python dependencies ready

REM Install frontend dependencies
echo 📦 Installing Node.js dependencies...
cd ..\frontend
npm install >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Failed to install Node.js dependencies
    pause
    exit /b 1
)
echo ✅ Node.js dependencies ready

echo.
echo 🚀 Starting Application Components...

REM Start backend server
echo 🔧 Starting backend server...
cd ..\backend
start /B cmd /c "python start_backend.py"

REM Wait for backend to initialize
echo ⏳ Waiting for backend to initialize...
timeout /t 5 /nobreak >nul

REM Start frontend application
echo 🖥️  Starting frontend application...
cd ..\frontend
start /B cmd /c "npm start"

REM Wait for frontend to start
echo ⏳ Waiting for frontend to start...
timeout /t 3 /nobreak >nul

echo.
echo ████████████████████████████████████████████████████████████████
echo ██                                                            ██
echo ██           🎉 CASH REGISTER PRO IS NOW RUNNING! 🎉          ██
echo ██                                                            ██
echo ████████████████████████████████████████████████████████████████
echo.
echo 📍 Backend API: http://localhost:5000
echo 🖥️  Frontend: Electron Desktop Application
echo.
echo 🔐 DEFAULT LOGIN CREDENTIALS:
echo    👑 Admin: username=admin, password=admin123
echo    👤 Cashier: username=cashier, password=cashier123
echo.
echo ✨ FEATURES INCLUDED:
echo    ✅ Complete Point-of-Sale System
echo    ✅ Table Management with Visual Cards
echo    ✅ Menu and Category Management
echo    ✅ Order Processing with Real-time Calculations
echo    ✅ Admin Dashboard for System Management
echo    ✅ Real-time Clock Display (Bottom Right)
echo    ✅ Professional UI/UX Design
echo    ✅ Touch/Mouse/Keyboard Accessibility
echo    ✅ All Cache and Database Errors Fixed
echo    ✅ Timestamps on Everything
echo    ✅ Local Database (SQLite)
echo.
echo 🎯 WHAT YOU CAN DO:
echo    📋 Admin: Manage users, tables, menu items, view reports
echo    🛒 Cashier: Take orders, process payments, manage tables
echo    🪑 Tables: Click table cards to start taking orders
echo    🍽️  Menu: Add items by category with real-time totals
echo    💰 Orders: Automatic tax calculation (8.25%)
echo    🕐 Clock: Real-time display in bottom right corner
echo.
echo ⚠️  IMPORTANT: Keep this window open while using the application
echo 🔄 To stop: Close the Electron app window or press Ctrl+C here
echo.
echo ████████████████████████████████████████████████████████████████

REM Keep the window open and monitor
:MONITOR
timeout /t 5 /nobreak >nul
tasklist /fi "imagename eq CashRegisterApp.exe" 2>nul | find /i "CashRegisterApp.exe" >nul
if %errorlevel% neq 0 (
    tasklist /fi "imagename eq electron.exe" 2>nul | find /i "electron.exe" >nul
    if %errorlevel% neq 0 (
        echo.
        echo 🔄 Application closed. Cleaning up...
        goto CLEANUP
    )
)
goto MONITOR

:CLEANUP
echo 🧹 Cleaning up processes...
taskkill /f /im python.exe 2>nul
taskkill /f /im node.exe 2>nul
echo ✅ Cleanup completed
echo.
echo 👋 Thank you for using Cash Register Pro!
echo.
pause
exit
